"""
Main entry point for the ManagerPort RAG Chatbot.

This script initializes and runs the Gradio interface for the chatbot.
"""

import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from rag_chatbot.config import Config
from rag_chatbot.config_validator import ConfigValidator
from rag_chatbot.utils.logger import logger
from rag_chatbot.pdf_processor import PDFProcessor
from rag_chatbot.vector_store import VectorStore
from rag_chatbot.rag_pipeline import RAGPipeline


def main():
    """Main function to run the RAG chatbot."""
    try:
        logger.info("ManagerPort RAG Chatbot starting...")

        # Display project status
        print("🚀 ManagerPort RAG Chatbot")
        print("=" * 50)

        # Validate configuration
        print("\n🔧 Validating configuration...")
        validator = ConfigValidator()
        is_valid, report = validator.validate_all()

        if not is_valid:
            print("❌ Configuration validation failed!")
            validator.print_report(report)
            return

        print("✅ Configuration is valid")
        print(f"📁 PDF files found: {len(Config.get_pdf_files())}")

        # Create necessary directories
        Config.DATA_DIR.mkdir(exist_ok=True)
        Config.LOGS_DIR.mkdir(exist_ok=True)

        # Initialize and run PDF processing
        print("\n📄 Processing PDF documents...")
        processor = PDFProcessor()
        processed_docs = processor.process_all_pdfs()

        if processed_docs:
            total_chars = sum(doc['processing_stats']['cleaned_char_count'] for doc in processed_docs.values())
            total_pages = sum(doc['metadata']['pages_with_text'] for doc in processed_docs.values())

            print(f"✅ Successfully processed {len(processed_docs)} documents")
            print(f"📊 Total: {total_chars:,} characters from {total_pages} pages")

            # Save processed texts
            output_dir = processor.save_processed_text()
            print(f"💾 Processed texts saved to: {output_dir}")

            # Check if we can proceed with vector store setup
            if Config.OPENAI_API_KEY:
                print("\n🗄️ Setting up vector database...")
                try:
                    vector_store = VectorStore()
                    collection = vector_store.get_or_create_collection()

                    # Check if collection is empty
                    if collection.count() == 0:
                        print("📝 Chunking and indexing documents...")
                        chunks = vector_store.chunk_documents(processed_docs)
                        success = vector_store.add_documents_to_collection(chunks)

                        if success:
                            print(f"✅ Successfully indexed {len(chunks)} document chunks")
                        else:
                            print("❌ Failed to index documents")
                    else:
                        print(f"ℹ️ Vector database already contains {collection.count()} documents")

                    # Display collection stats
                    stats = vector_store.get_collection_stats()
                    print(f"📊 Vector Database: {stats.get('total_documents', 0)} chunks from {stats.get('unique_source_files', 0)} files")

                    # Initialize RAG pipeline
                    print("\n🤖 Initializing RAG pipeline...")
                    try:
                        rag = RAGPipeline()
                        rag_info = rag.get_pipeline_info()
                        print(f"✅ RAG pipeline ready!")
                        print(f"📊 Model: {rag_info['model']}")
                        print(f"📊 Temperature: {rag_info['temperature']}")
                        print(f"📊 Retrieval K: {rag_info['retrieval_k']}")

                        # Test with a sample question
                        print("\n🧪 Testing RAG pipeline...")
                        test_response = rag.ask_question("Čo je projektový manažment?")
                        print(f"✅ Test successful - Answer length: {len(test_response['answer'])} characters")
                        print(f"📚 Sources found: {len(test_response['sources'])}")

                        print("\n🎯 Next steps:")
                        print("  ✅ RAG pipeline ready!")
                        print("  ✅ Gradio interface available!")
                        print("  - Run 'python app.py' to start the chat interface")
                        print("  - Testing and validation")
                        print("  - Docker containerization")

                    except Exception as e:
                        logger.error(f"RAG pipeline initialization failed: {e}")
                        print(f"❌ RAG pipeline failed: {e}")
                        print("\n🎯 Next steps:")
                        print("  - Fix RAG pipeline issues")
                        print("  - Gradio chat interface")
                        print("  - Testing and validation")

                except Exception as e:
                    logger.error(f"Vector store setup failed: {e}")
                    print(f"❌ Vector store setup failed: {e}")
                    print("\n🎯 Next steps:")
                    print("  - Fix vector database issues")
                    print("  - RAG pipeline implementation")
                    print("  - Gradio chat interface")
            else:
                print("\n⚠️ OpenAI API key required for vector database setup")
                print("   Copy .env.example to .env and add your API key")
                print("\n🎯 Next steps:")
                print("  - Set up OpenAI API key")
                print("  - Vector database setup (Chroma)")
                print("  - Document chunking and embedding")
                print("  - RAG pipeline implementation")
                print("  - Gradio chat interface")
        else:
            print("❌ No documents were processed successfully")

    except Exception as e:
        logger.error(f"Failed to start chatbot: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
