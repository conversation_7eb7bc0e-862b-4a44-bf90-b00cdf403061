# Markdown Support and Database Reset Implementation

This document describes the new features added to the ManagerPort RAG Chatbot to support markdown files and database reset functionality.

## 🆕 New Features

### 1. Markdown File Support

The system now supports markdown files from the `assets/md` directory in addition to PDF files.

#### What's New:
- **MarkdownProcessor**: New processor class for handling `.md` files
- **Unified DocumentProcessor**: Combines PDF and Markdown processing
- **Enhanced Configuration**: Added `MD_DIR` configuration pointing to `assets/md`
- **Automatic Detection**: System automatically finds and processes all `.md` files

#### Supported Features:
- ✅ Markdown syntax cleaning (headers, links, emphasis, code blocks, lists)
- ✅ Slovak/Czech character normalization
- ✅ Text chunking and embedding generation
- ✅ Integration with existing RAG pipeline
- ✅ Source file tracking in responses

### 2. Database Reset and Upgrade Functionality

New database management capabilities for deployment and maintenance.

#### What's New:
- **Database Reset**: Complete vector database reset and reload
- **Batch Processing**: Improved embedding generation with API limits handling
- **Management Script**: Dedicated database management tool
- **Deployment Integration**: Reset commands integrated into deployment script

## 📁 File Structure

```
assets/
├── pdf/          # PDF files (existing)
└── md/           # Markdown files (NEW)
    ├── 2017 vsetky STRETNUTIA A TEMY_MANAGERPORT komunity_final.md
    ├── 2018 STRETNUTIA A TEMY myPMCom PM Community_c.md
    ├── 2019 vsetky STRETNUTIA A TEMY myPMCom PM Community_d -cast1.md
    ├── 2019 vsetky STRETNUTIA A TEMY myPMCom PM Community_d -cast2.md
    ├── 2020 vsetky STRETNUTIA A TEMY_MANAGERPORT komunity.md
    ├── 2021 vsetky STRETNUTIA A TEMY_MANAGERPORT komunity_final4.md
    ├── 2022 vsetky STRETNUTIA A TEMY_MANAGERPORT komunity.md
    ├── 2023 ROCENKA MANAGERPORT komunity.md
    ├── 2024 ROCENKA MANAGERPORT komunita.md
    ├── 2025 stretnutia MANAGERPORT JAN_JUN.md
    ├── 2025 stretnutia MANAGERPORT JAN_JUN_ver2.md
    ├── Slovnik_Projektoveho riadenia_v6.3a MANAGERPORT.md
    └── Vsetky Mudrovacky 2017 az 2025.md

scripts/
├── deploy.sh              # Enhanced deployment script
└── manage_database.py     # NEW: Database management script

src/rag_chatbot/
├── markdown_processor.py  # NEW: Markdown file processor
├── document_processor.py  # NEW: Unified document processor
├── config.py              # Enhanced with MD_DIR
└── vector_store.py        # Enhanced with reset functionality
```

## 🛠️ Usage

### Database Management

#### Check Database Status
```bash
python3 scripts/manage_database.py status
```

#### Reset Database and Reload All Documents
```bash
python3 scripts/manage_database.py reset
```

#### Process Documents Only (No Database Update)
```bash
python3 scripts/manage_database.py process
```

### Deployment with Database Reset

#### Development with Database Reset
```bash
bash scripts/deploy.sh dev-reset
```

#### Production with Database Reset
```bash
bash scripts/deploy.sh prod-reset
```

#### Reset Database Only
```bash
bash scripts/deploy.sh reset-db
```

### Regular Usage

The system now automatically processes both PDF and Markdown files:

```bash
# Regular initialization (processes both PDF and MD files)
python3 main.py

# Start the chat application
python3 app.py
```

## 📊 Current Status

After implementation, the system now processes:

- **PDF Files**: 8 files from `assets/pdf/`
- **Markdown Files**: 13 files from `assets/md/`
- **Total Documents**: 21 files
- **Total Characters**: ~1.9M characters
- **Vector Database**: ~1,656 chunks (when only MD files are processed)

## 🔧 Technical Details

### Markdown Processing Features

1. **Syntax Cleaning**:
   - Removes markdown headers (`#`, `##`, etc.) but keeps text
   - Converts links `[text](url)` to just `text`
   - Removes emphasis markers (`**bold**`, `*italic*`) but keeps text
   - Cleans code blocks and inline code
   - Removes list markers but keeps content

2. **Text Normalization**:
   - Slovak/Czech character normalization
   - Whitespace cleanup
   - URL and email removal
   - Control character removal

3. **Metadata Extraction**:
   - File size, creation/modification dates
   - Line counts and character counts
   - Header detection and section estimation

### Database Reset Features

1. **Batch Processing**:
   - Embedding generation in batches of 200 texts
   - Handles OpenAI API token limits (300k tokens/request)
   - Progress tracking and error handling

2. **Complete Reset**:
   - Deletes existing vector collection
   - Recreates collection with fresh metadata
   - Processes all available documents (PDF + MD)
   - Generates new embeddings and stores chunks

## 🧪 Testing

The implementation has been tested with:

1. **Document Processing**: ✅ Both PDF and Markdown files processed correctly
2. **Database Reset**: ✅ Complete reset and reload functionality working
3. **RAG Functionality**: ✅ Questions answered using markdown content
4. **Deployment Scripts**: ✅ New commands available and working
5. **Error Handling**: ✅ API limits and batch processing handled correctly

## 🚀 Next Steps

1. **Add More Document Types**: Consider adding support for other formats (TXT, DOCX, etc.)
2. **Enhanced Metadata**: Add more sophisticated document metadata extraction
3. **Incremental Updates**: Implement incremental document updates instead of full resets
4. **Monitoring**: Add monitoring for document processing and database health
5. **Backup/Restore**: Implement database backup and restore functionality

## 📝 Notes

- The system maintains backward compatibility with existing PDF processing
- All existing functionality continues to work unchanged
- New features are additive and don't break existing workflows
- Database reset is safe and can be run multiple times
- Processed texts are saved to `data/processed_texts/` for inspection
