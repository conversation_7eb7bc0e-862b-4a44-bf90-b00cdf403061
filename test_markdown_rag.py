#!/usr/bin/env python3
"""Test script to verify markdown files are working in RAG system."""

import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from rag_chatbot.config import Config
from rag_chatbot.rag_pipeline import RAGPipeline
from rag_chatbot.utils.logger import logger


def test_markdown_rag():
    """Test RAG functionality with markdown files."""
    print("🧪 Testing RAG with Markdown files...")
    
    try:
        # Check if we have an API key
        if not Config.OPENAI_API_KEY:
            print("❌ OpenAI API key not found!")
            print("   Please set OPENAI_API_KEY in your .env file")
            return False
        
        # Initialize RAG pipeline
        print("🤖 Initializing RAG pipeline...")
        rag = RAGPipeline()
        
        # Test questions that should be answerable from markdown files
        test_questions = [
            "Čo je projektový manažment?",
            "<PERSON><PERSON><PERSON> s<PERSON> hlavné témy MANAGERPORT komunity?",
            "Čo obsahuje slovník projektového riadenia?",
            "<PERSON>k<PERSON> stretnutia sa konali v roku 2024?",
            "Čo sú to mudrovačky?"
        ]
        
        print(f"\n📝 Testing {len(test_questions)} questions...")
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n--- Test {i}/{len(test_questions)} ---")
            print(f"❓ Question: {question}")
            
            try:
                response = rag.ask_question(question)
                
                print(f"✅ Answer: {response['answer'][:200]}...")
                print(f"📚 Sources: {len(response['sources'])} documents")
                print(f"🔍 Retrieved: {response['retrieved_documents']} chunks")
                
                # Show source files
                if response['sources']:
                    source_files = set()
                    for source in response['sources']:
                        if 'source_file' in source:
                            source_files.add(source['source_file'])
                    print(f"📄 Source files: {', '.join(list(source_files)[:3])}...")
                
            except Exception as e:
                print(f"❌ Error: {e}")
                return False
        
        print("\n✅ All tests completed successfully!")
        print("🎉 Markdown files are working correctly in the RAG system!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.error(f"RAG test failed: {e}")
        return False


if __name__ == "__main__":
    success = test_markdown_rag()
    sys.exit(0 if success else 1)
