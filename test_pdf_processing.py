"""Test script for PDF processing functionality."""

import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from rag_chatbot.pdf_processor import PDFProcessor
from rag_chatbot.config import Config
from rag_chatbot.utils.logger import logger


def main():
    """Test PDF processing functionality."""
    try:
        logger.info("🧪 Testing PDF Processing Module")
        
        # Initialize processor
        processor = PDFProcessor()
        
        # Process all PDFs
        processed_docs = processor.process_all_pdfs()
        
        if not processed_docs:
            logger.error("No documents were processed successfully")
            return
        
        # Display summary
        print("\n" + "="*60)
        print("📊 PDF PROCESSING SUMMARY")
        print("="*60)
        
        total_chars = 0
        total_pages = 0
        
        for filename, doc_data in processed_docs.items():
            metadata = doc_data['metadata']
            stats = doc_data['processing_stats']
            
            print(f"\n📄 {filename}")
            print(f"   Pages: {metadata['pages_with_text']}")
            print(f"   Characters: {stats['cleaned_char_count']:,}")
            print(f"   Compression: {stats['compression_ratio']:.2%}")
            
            total_chars += stats['cleaned_char_count']
            total_pages += metadata['pages_with_text']
        
        print(f"\n🎯 TOTALS:")
        print(f"   Documents: {len(processed_docs)}")
        print(f"   Total Pages: {total_pages}")
        print(f"   Total Characters: {total_chars:,}")
        print(f"   Average per Document: {total_chars // len(processed_docs):,} chars")
        
        # Save processed texts for inspection
        output_dir = processor.save_processed_text()
        print(f"\n💾 Processed texts saved to: {output_dir}")
        
        # Show sample from first document
        first_doc = next(iter(processed_docs.values()))
        sample_text = first_doc['cleaned_text'][:500]
        print(f"\n📝 Sample text from {first_doc['filename']}:")
        print("-" * 50)
        print(sample_text + "..." if len(first_doc['cleaned_text']) > 500 else sample_text)
        
        logger.info("✅ PDF processing test completed successfully")
        
    except Exception as e:
        logger.error(f"❌ PDF processing test failed: {e}")
        raise


if __name__ == "__main__":
    main()
