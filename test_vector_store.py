"""Test script for vector store functionality."""

import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from rag_chatbot.vector_store import VectorStore
from rag_chatbot.pdf_processor import PDFProcessor
from rag_chatbot.config import Config
from rag_chatbot.utils.logger import logger


def main():
    """Test vector store functionality."""
    try:
        logger.info("🧪 Testing Vector Store Module")
        
        # Check if we have an API key
        if not Config.OPENAI_API_KEY:
            print("❌ OpenAI API key not found. Please set OPENAI_API_KEY in .env file")
            print("   Copy .env.example to .env and add your API key")
            return
        
        print("🔑 OpenAI API key found")
        
        # Initialize PDF processor and get documents
        print("\n📄 Loading processed documents...")
        processor = PDFProcessor()
        processed_docs = processor.process_all_pdfs()
        
        if not processed_docs:
            print("❌ No processed documents found")
            return
        
        print(f"✅ Loaded {len(processed_docs)} documents")
        
        # Initialize vector store
        print("\n🗄️ Initializing vector store...")
        vector_store = VectorStore()
        
        # Get or create collection
        collection = vector_store.get_or_create_collection()
        
        # Check if collection already has documents
        current_count = collection.count()
        print(f"📊 Current collection has {current_count} documents")
        
        if current_count == 0:
            print("\n📝 Chunking documents...")
            chunks = vector_store.chunk_documents(processed_docs)
            
            print(f"✅ Created {len(chunks)} chunks")
            
            # Show sample chunk
            if chunks:
                sample_chunk = chunks[0]
                print(f"\n📋 Sample chunk from {sample_chunk['metadata']['source_file']}:")
                print("-" * 60)
                print(sample_chunk['text'][:300] + "..." if len(sample_chunk['text']) > 300 else sample_chunk['text'])
                print("-" * 60)
            
            print("\n🔄 Adding documents to vector collection...")
            success = vector_store.add_documents_to_collection(chunks)
            
            if success:
                print("✅ Documents successfully added to vector store")
            else:
                print("❌ Failed to add documents to vector store")
                return
        else:
            print("ℹ️ Collection already contains documents, skipping indexing")
        
        # Get collection statistics
        print("\n📊 Collection Statistics:")
        stats = vector_store.get_collection_stats()
        for key, value in stats.items():
            if key == 'source_files':
                print(f"  {key}: {', '.join(value) if value else 'None'}")
            else:
                print(f"  {key}: {value}")
        
        # Test search functionality
        print("\n🔍 Testing search functionality...")
        
        test_queries = [
            "manažment projektov",
            "vodcovstvo a leadership",
            "teamwork a spolupráca",
            "agile metodiky",
            "komunikácia v tíme"
        ]
        
        for query in test_queries:
            print(f"\n🔎 Searching for: '{query}'")
            results = vector_store.search_similar_documents(query, n_results=3)
            
            if results:
                for i, result in enumerate(results, 1):
                    similarity = result['similarity_score']
                    source = result['metadata']['source_file']
                    chunk_idx = result['metadata']['chunk_index']
                    text_preview = result['text'][:150] + "..." if len(result['text']) > 150 else result['text']
                    
                    print(f"  {i}. Similarity: {similarity:.3f} | Source: {source} | Chunk: {chunk_idx}")
                    print(f"     Preview: {text_preview}")
            else:
                print("  No results found")
        
        print("\n✅ Vector store test completed successfully!")
        print(f"\n💾 Vector database location: {Config.VECTOR_DB_PATH}")
        print(f"📊 Total documents in collection: {vector_store.collection.count()}")
        
    except Exception as e:
        logger.error(f"❌ Vector store test failed: {e}")
        raise


if __name__ == "__main__":
    main()
