"""Test script for model selection functionality."""

import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from rag_chatbot.model_selector import ModelSelector
from rag_chatbot.config import Config


def test_model_selector():
    """Test the model selector functionality."""
    print("🤖 Testing Model Selection Functionality")
    print("=" * 60)
    
    selector = ModelSelector()
    
    # Test 1: List available models
    print("\n1. Available Models:")
    available = selector.list_available_models()
    print(f"   Found {len(available)} models")
    
    # Test 2: List recommended models
    print("\n2. Recommended Models:")
    recommended = selector.list_recommended_models()
    for model_id, info in recommended.items():
        print(f"   ✅ {info['name']} ({model_id})")
    
    # Test 3: Model comparison
    print("\n3. Model Comparison:")
    selector.print_model_comparison(list(recommended.keys()))
    
    # Test 4: Test model selection
    print("\n4. Testing Model Selection:")
    test_model = "gpt-4o-mini"
    if selector.select_model(test_model):
        print(f"   ✅ Successfully selected: {test_model}")
        print(f"   Current model: {selector.get_current_model()}")
    else:
        print(f"   ❌ Failed to select: {test_model}")
    
    # Test 5: Cost estimation
    print("\n5. Cost Estimation:")
    for model_id in ["gpt-4o", "gpt-4o-mini", "gpt-3.5-turbo"]:
        cost = selector.estimate_cost(model_id, 1000, 500)
        print(f"   {model_id}: ${cost:.4f} (1k input + 500 output tokens)")
    
    # Test 6: Budget-based selection
    print("\n6. Budget-Based Selection:")
    budget_model = selector.get_best_model_for_budget(0.01)  # $0.01 per 1k tokens
    if budget_model:
        print(f"   Best model for budget: {budget_model}")
        selector.print_model_details(budget_model)
    else:
        print("   No model found within budget")
    
    print("\n✅ Model selection tests completed!")


def test_config_integration():
    """Test integration with Config class."""
    print("\n🔧 Testing Config Integration")
    print("=" * 40)
    
    # Test model info methods
    print(f"Current model: {Config.OPENAI_MODEL}")
    print(f"Available models: {len(Config.get_available_models())}")
    print(f"Recommended models: {len(Config.get_recommended_models())}")
    
    # Test model validation
    test_models = ["gpt-4o", "invalid-model", "gpt-3.5-turbo"]
    for model in test_models:
        is_valid = Config.is_valid_model(model)
        print(f"Model '{model}': {'✅ Valid' if is_valid else '❌ Invalid'}")
    
    print("✅ Config integration tests completed!")


def main():
    """Run all model selection tests."""
    try:
        test_model_selector()
        test_config_integration()
        
        print("\n🎉 All tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
