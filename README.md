# ManagerPort RAG Chatbot

An AI-powered chatbot for the ManagerPort community using Retrieval-Augmented Generation (RAG) with PDF documents as the knowledge source.

## 🎯 Project Overview

This project creates an intelligent chatbot that can answer questions about ManagerPort community content by processing PDF documents from 2017-2024 containing meeting summaries, topics, and community activities.

## 🏗️ Architecture

- **PDF Processing**: PyMuPDF for text extraction from community documents
- **Embeddings**: OpenAI text-embedding-ada-002 for semantic search
- **Vector Database**: Chroma for local vector storage and retrieval
- **RAG Framework**: LangChain for orchestrating the retrieval-augmented generation pipeline
- **LLM**: OpenAI GPT-4 for response generation
- **Frontend**: Gradio for intuitive chat interface
- **Deployment**: Docker with nginx-proxy-manager integration

## 📁 Project Structure

```
managerport-chatbot/
├── assets/pdf/                 # PDF documents (8 files, 2017-2024)
├── src/rag_chatbot/           # Main application code
│   ├── __init__.py
│   ├── config.py              # Configuration management
│   └── utils/
│       ├── __init__.py
│       └── logger.py          # Logging setup
├── data/                      # Vector database storage
├── logs/                      # Application logs
├── tests/                     # Unit tests
├── requirements.txt           # Python dependencies
├── .env.example              # Environment variables template
├── main.py                   # Application entry point
└── README.md                 # This file
```

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- OpenAI API key

### Installation

1. **Clone and setup environment:**

   ```bash
   git clone <repository-url>
   cd managerport-chatbot
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. **Install dependencies:**

   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment:**

   ```bash
   cp .env.example .env
   # Edit .env and add your OpenAI API key
   ```

4. **Launch the chat interface:**

   ```bash
   python app.py
   ```

   The web interface will be available at: http://localhost:7860

5. **Alternative - Run setup and testing:**
   ```bash
   python main.py
   ```

## 🐳 Docker Deployment

For production deployment with Docker:

1. **Install Docker with Compose plugin**

2. **Configure environment:**

   ```bash
   cp .env.example .env
   # Edit .env and add your OpenAI API key
   ```

3. **Deploy:**

   ```bash
   # Development
   ./scripts/deploy.sh dev

   # Production with Nginx Proxy Manager
   ./scripts/deploy.sh prod

   # Production with monitoring
   ./scripts/deploy.sh monitoring
   ```

4. **Access:**
   - Chatbot: http://localhost:7860
   - Nginx Proxy Manager: http://localhost:81
   - Uptime Kuma (monitoring): http://localhost:3001

See [DOCKER_DEPLOYMENT.md](DOCKER_DEPLOYMENT.md) for detailed deployment instructions.

## ✨ Features

- **🤖 Intelligent Chat Interface**: Beautiful Gradio web interface with Slovak language support
- **🔄 Model Selection**: Choose from latest OpenAI models (GPT-4o, GPT-4o Mini, GPT-4 Turbo, etc.)
- **📚 RAG-Powered Responses**: Retrieval-Augmented Generation using OpenAI models
- **🔍 Semantic Search**: Advanced vector search through 776 document chunks
- **📖 Source Citations**: Responses include source documents and similarity scores
- **💡 Example Questions**: Pre-built questions to get started quickly
- **🎯 Management Focus**: Specialized in project management, leadership, and team dynamics
- **📊 Real-time Stats**: Live information about the knowledge base
- **🐳 Docker Ready**: Complete containerization with production deployment support

## 📄 Data Sources

The chatbot processes 8 PDF documents containing ManagerPort community content:

- **2017**: Community meetings and topics
- **2018**: Community meetings and topics
- **2019**: All meetings and topics
- **2020**: All meetings and topics
- **2021**: Annual report
- **2022**: All meetings and topics
- **2023**: Community annual report
- **2024**: Community annual report

## 🔧 Configuration

Key configuration options in `.env`:

```env
# OpenAI Configuration
OPENAI_API_KEY=your_api_key_here
EMBEDDING_MODEL=text-embedding-ada-002
LLM_MODEL=gpt-4-turbo-preview

# Vector Database
VECTOR_DB_PATH=./data/chroma_db
COLLECTION_NAME=managerport_docs

# Text Processing
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# Gradio Interface
GRADIO_SERVER_PORT=7860
```

## 🐳 Docker Deployment

The project includes Docker configuration for deployment with nginx-proxy-manager:

```bash
docker compose up -d
```

## 📊 Development Status

- [x] **Project Setup** - Environment and dependencies configured
- [x] **PDF Processing** - Text extraction from PDF files (8 files, 613,897 characters, 309 pages)
- [x] **Vector Database** - Chroma setup and embedding generation
- [/] **Document Indexing** - Text chunking and vector storage (in progress)
- [ ] **RAG Pipeline** - LangChain workflow implementation
- [ ] **Gradio Interface** - Web-based chat UI
- [ ] **Configuration** - Environment management
- [ ] **Docker Setup** - Containerization for deployment
- [ ] **Testing** - Unit tests and validation
- [ ] **Documentation** - Complete setup and usage guide

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.
