"""Test script to verify Docker Compose commands are updated correctly."""

import re
from pathlib import Path


def test_deploy_script():
    """Test that deploy.sh uses new docker compose commands."""
    print("🐳 Testing deploy.sh for updated Docker Compose commands")
    print("=" * 60)
    
    script_path = Path("scripts/deploy.sh")
    if not script_path.exists():
        print("❌ scripts/deploy.sh not found")
        return False
    
    content = script_path.read_text()
    
    # Check for old docker-compose commands
    old_commands = [
        "docker-compose up",
        "docker-compose down", 
        "docker-compose build",
        "docker-compose ps",
        "docker-compose logs"
    ]
    
    # Check for new docker compose commands
    new_commands = [
        "docker compose up",
        "docker compose down",
        "docker compose build", 
        "docker compose ps",
        "docker compose logs"
    ]
    
    found_old = []
    for cmd in old_commands:
        if cmd in content:
            found_old.append(cmd)
    
    found_new = 0
    for cmd in new_commands:
        if cmd in content:
            found_new += 1
    
    if found_old:
        print(f"❌ Found old commands: {found_old}")
        return False
    
    if found_new < len(new_commands):
        print(f"⚠️ Only found {found_new}/{len(new_commands)} new commands")
    
    # Check for docker-compose prerequisite check
    if "docker-compose" in content and "command_exists docker-compose" in content:
        print("❌ Still checking for docker-compose command")
        return False
    
    print(f"✅ Found {found_new} new docker compose commands")
    print("✅ No old docker-compose commands found")
    print("✅ Updated prerequisite check")
    return True


def test_documentation():
    """Test that documentation uses new docker compose commands."""
    print("\n📚 Testing documentation for updated Docker Compose commands")
    print("=" * 60)
    
    doc_files = ["DOCKER_DEPLOYMENT.md", "README.md"]
    
    for doc_file in doc_files:
        print(f"\n  Testing {doc_file}...")
        
        doc_path = Path(doc_file)
        if not doc_path.exists():
            print(f"❌ {doc_file} not found")
            return False
        
        content = doc_path.read_text()
        
        # Count old vs new commands
        old_count = len(re.findall(r'docker-compose\s+\w+', content))
        new_count = len(re.findall(r'docker compose\s+\w+', content))
        
        if old_count > 0:
            print(f"❌ Found {old_count} old docker-compose commands")
            return False
        
        if new_count == 0:
            print(f"⚠️ No docker compose commands found")
        else:
            print(f"✅ Found {new_count} new docker compose commands")
    
    return True


def test_compose_files():
    """Test that compose files are valid."""
    print("\n🔧 Testing Docker Compose files")
    print("=" * 40)
    
    compose_files = ["docker-compose.yml", "docker-compose.prod.yml"]
    
    for compose_file in compose_files:
        print(f"\n  Testing {compose_file}...")
        
        compose_path = Path(compose_file)
        if not compose_path.exists():
            print(f"❌ {compose_file} not found")
            return False
        
        try:
            import yaml
            with open(compose_path, 'r') as f:
                config = yaml.safe_load(f)
            
            # Check version
            version = config.get('version', '')
            if version.startswith('3.'):
                print(f"✅ Valid Compose file version: {version}")
            else:
                print(f"⚠️ Compose file version: {version}")
            
            # Check services
            services = config.get('services', {})
            if services:
                print(f"✅ Found {len(services)} services")
            else:
                print("❌ No services found")
                return False
                
        except Exception as e:
            print(f"❌ Error parsing {compose_file}: {e}")
            return False
    
    return True


def test_command_compatibility():
    """Test command compatibility information."""
    print("\n🔄 Testing command compatibility")
    print("=" * 40)
    
    print("Docker Compose command evolution:")
    print("  Old: docker-compose (standalone binary)")
    print("  New: docker compose (Docker CLI plugin)")
    print("")
    print("Benefits of new command:")
    print("  ✅ Integrated with Docker CLI")
    print("  ✅ Better error messages")
    print("  ✅ Consistent with Docker ecosystem")
    print("  ✅ Active development and support")
    print("")
    print("Compatibility:")
    print("  • Docker Desktop: Includes compose plugin by default")
    print("  • Linux: Install docker-compose-plugin package")
    print("  • Fallback: Old docker-compose still works but deprecated")
    
    return True


def main():
    """Run all Docker command tests."""
    print("🧪 Docker Compose Command Update Validation")
    print("=" * 70)
    
    tests = [
        ("Deploy Script", test_deploy_script),
        ("Documentation", test_documentation),
        ("Compose Files", test_compose_files),
        ("Command Compatibility", test_command_compatibility),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        print("\n✅ Successfully updated to modern Docker Compose commands!")
        print("\n📋 Summary of changes:")
        print("  • Updated scripts/deploy.sh")
        print("  • Updated DOCKER_DEPLOYMENT.md")
        print("  • Updated README.md")
        print("  • Changed docker-compose → docker compose")
        print("  • Updated prerequisite checks")
        print("\n🚀 Ready for deployment with modern Docker!")
    else:
        print("⚠️ Some tests failed. Please fix the issues above.")
    
    return passed == total


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
