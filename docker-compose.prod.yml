version: '3.8'

services:
  managerport-chatbot:
    build: .
    container_name: managerport-chatbot-prod
    restart: unless-stopped
    environment:
      # OpenAI Configuration
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_MODEL=${OPENAI_MODEL:-gpt-4.1-nano}
      - EMBEDDING_MODEL=${EMBEDDING_MODEL:-text-embedding-ada-002}
      - TEMPERATURE=${TEMPERATURE:-0.1}
      
      # Vector Database Configuration
      - VECTOR_DB_PATH=/app/data/chroma_db
      - COLLECTION_NAME=${COLLECTION_NAME:-managerport_docs}
      
      # Text Processing Configuration
      - CHUNK_SIZE=${CHUNK_SIZE:-1000}
      - CHUNK_OVERLAP=${CHUNK_OVERLAP:-200}
      
      # RAG Configuration
      - RETRIEVAL_K=${RETRIEVAL_K:-5}
      
      # Gradio Configuration
      - <PERSON><PERSON><PERSON><PERSON>_SERVER_NAME=0.0.0.0
      - GRADIO_SERVER_PORT=7860
      - GRADIO_SHARE=false
      
      # Logging
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    
    # Only expose to internal network (nginx-proxy-manager will handle external access)
    expose:
      - "7860"
    
    volumes:
      # Persistent data storage
      - chatbot_data:/app/data
      - chatbot_logs:/app/logs
      # Mount PDF files (read-only)
      - ./assets/pdf:/app/assets/pdf:ro
    
    networks:
      - proxy-network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7860/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    labels:
      # Labels for nginx-proxy-manager auto-discovery (if using traefik-style setup)
      - "traefik.enable=true"
      - "traefik.http.routers.managerport-chatbot.rule=Host(`chatbot.yourdomain.com`)"
      - "traefik.http.routers.managerport-chatbot.tls=true"
      - "traefik.http.routers.managerport-chatbot.tls.certresolver=letsencrypt"

  # nginx-proxy-manager:
  #   image: 'jc21/nginx-proxy-manager:latest'
  #   container_name: nginx-proxy-manager
  #   restart: unless-stopped
  #   ports:
  #     # HTTP
  #     - '80:80'
  #     # HTTPS
  #     - '443:443'
  #     # Admin UI
  #     - '81:81'
  #   volumes:
  #     - npm_data:/data
  #     - npm_letsencrypt:/etc/letsencrypt
  #   networks:
  #     - proxy-network
  #   environment:
  #     # Optional: Set timezone
  #     - TZ=Europe/Bratislava

  # Optional: Watchtower for automatic updates
  watchtower:
    image: containrrr/watchtower
    container_name: watchtower
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - WATCHTOWER_CLEANUP=true
      - WATCHTOWER_POLL_INTERVAL=3600  # Check every hour
      - WATCHTOWER_INCLUDE_STOPPED=true
    networks:
      - proxy-network

    profiles:
      - autoupdate

  # Optional: Monitoring with Uptime Kuma
  uptime-kuma:
    image: louislam/uptime-kuma:1
    container_name: uptime-kuma
    restart: unless-stopped
    ports:
      - "3001:3001"
    volumes:
      - uptime_data:/app/data
    networks:
      - proxy-network
    profiles:
      - monitoring

volumes:
  chatbot_data:
    driver: local
  chatbot_logs:
    driver: local
  # npm_data:
  #   driver: local
  # npm_letsencrypt:
  #   driver: local
  uptime_data:
    driver: local

networks:
  proxy-network:
    # driver: bridge
    external: true
