#!/bin/bash

# ManagerPort RAG Chatbot Deployment Script
# This script helps deploy the chatbot using Docker Compose

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! docker compose version >/dev/null 2>&1; then
        print_error "Docker Compose is not available. Please install Docker with Compose plugin."
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to check environment file
check_env_file() {
    print_status "Checking environment configuration..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            print_warning ".env file not found. Copying from .env.example"
            cp .env.example .env
            print_warning "Please edit .env file and add your OpenAI API key"
            print_warning "Then run this script again"
            exit 1
        else
            print_error ".env.example file not found"
            exit 1
        fi
    fi
    
    # Check if OpenAI API key is set
    if grep -q "your_openai_api_key_here" .env; then
        print_error "Please set your OpenAI API key in .env file"
        exit 1
    fi
    
    print_success "Environment configuration check passed"
}

# Function to deploy development environment
deploy_dev() {
    print_status "Deploying development environment..."

    # Build and start services
    docker compose down --remove-orphans
    docker compose build --no-cache
    docker compose up -d

    print_success "Development environment deployed"
    print_status "Chatbot will be available at: http://localhost:7860"
    print_status "Use 'docker compose logs -f' to view logs"
}

# Function to deploy production environment
deploy_prod() {
    print_status "Deploying production environment..."

    # Build and start services
    docker compose -f docker-compose.prod.yml down --remove-orphans
    docker compose -f docker-compose.prod.yml build --no-cache
    docker compose -f docker-compose.prod.yml up -d

    print_success "Production environment deployed"
    print_status "Nginx Proxy Manager UI: http://localhost:81"
    print_status "Default credentials: <EMAIL> / changeme"
    print_warning "Please change default credentials immediately!"
}

# Function to deploy quick update to production
deploy_quick_update() {
    print_status "Performing quick update to production..."

    # Check if production containers are running
    if ! docker compose -f docker-compose.prod.yml ps | grep -q "managerport-chatbot-prod"; then
        print_error "Production environment is not running. Please use 'prod' command first."
        exit 1
    fi

    # Rebuild and update only the chatbot service
    docker compose -f docker-compose.prod.yml build managerport-chatbot
    docker compose -f docker-compose.prod.yml up -d managerport-chatbot

    print_success "Quick update deployed"
    print_status "Use 'docker compose -f docker-compose.prod.yml logs -f' to view logs"
}

# Function to deploy with monitoring
deploy_with_monitoring() {
    print_status "Deploying with monitoring..."

    docker compose -f docker-compose.prod.yml --profile monitoring down --remove-orphans
    docker compose -f docker-compose.prod.yml --profile monitoring build --no-cache
    docker compose -f docker-compose.prod.yml --profile monitoring up -d

    print_success "Production environment with monitoring deployed"
    print_status "Nginx Proxy Manager UI: http://localhost:81"
    print_status "Uptime Kuma monitoring: http://localhost:3001"
}

# Function to reset database
reset_database() {
    print_status "Resetting vector database..."

    # Check if Python script exists
    if [ ! -f "scripts/manage_database.py" ]; then
        print_error "Database management script not found"
        exit 1
    fi

    # Run database reset
    python3 scripts/manage_database.py reset

    if [ $? -eq 0 ]; then
        print_success "Database reset completed"
    else
        print_error "Database reset failed"
        exit 1
    fi
}

# Function to deploy with database reset
deploy_with_reset() {
    print_status "Deploying with database reset..."

    # First reset the database
    reset_database

    # Then deploy development environment
    deploy_dev
}

# Function to upgrade production with database reset
upgrade_prod_with_reset() {
    print_status "Upgrading production with database reset..."

    # Stop production environment
    docker compose -f docker-compose.prod.yml down

    # Reset database
    reset_database

    # Deploy production environment
    deploy_prod
}

# Function to show status
show_status() {
    print_status "Checking deployment status..."

    if docker compose ps | grep -q "managerport-chatbot"; then
        print_success "Development environment is running"
        docker compose ps
    elif docker compose -f docker-compose.prod.yml ps | grep -q "managerport-chatbot-prod"; then
        print_success "Production environment is running"
        docker compose -f docker-compose.prod.yml ps
    else
        print_warning "No deployment found"
    fi
}

# Function to show logs
show_logs() {
    if docker compose ps | grep -q "managerport-chatbot"; then
        docker compose logs -f managerport-chatbot
    elif docker compose -f docker-compose.prod.yml ps | grep -q "managerport-chatbot-prod"; then
        docker compose -f docker-compose.prod.yml logs -f managerport-chatbot-prod
    else
        print_error "No running deployment found"
        exit 1
    fi
}

# Function to stop deployment
stop_deployment() {
    print_status "Stopping deployment..."

    docker compose down
    docker compose -f docker-compose.prod.yml down

    print_success "Deployment stopped"
}

# Function to show help
show_help() {
    echo "ManagerPort RAG Chatbot Deployment Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  dev         Deploy development environment"
    echo "  prod        Deploy production environment"
    echo "  monitoring  Deploy production with monitoring"
    echo "  quick-update Quick update of Python source files to production"
    echo "  dev-reset   Deploy development with database reset"
    echo "  prod-reset  Deploy production with database reset"
    echo "  reset-db    Reset vector database only"
    echo "  status      Show deployment status"
    echo "  logs        Show application logs"
    echo "  stop        Stop all deployments"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 dev                    # Deploy for development"
    echo "  $0 prod                   # Deploy for production"
    echo "  $0 monitoring             # Deploy with monitoring"
    echo "  $0 quick-update           # Quick update of Python files"
    echo "  $0 dev-reset              # Deploy dev with database reset"
    echo "  $0 prod-reset             # Deploy prod with database reset"
    echo "  $0 reset-db               # Reset database only"
    echo "  $0 status                 # Check status"
    echo "  $0 logs                   # View logs"
    echo "  $0 stop                   # Stop deployment"
}

# Main script logic
main() {
    case "${1:-help}" in
        "dev")
            check_prerequisites
            check_env_file
            deploy_dev
            ;;
        "prod")
            check_prerequisites
            check_env_file
            deploy_prod
            ;;
        "monitoring")
            check_prerequisites
            check_env_file
            deploy_with_monitoring
            ;;
        "quick-update")
            check_prerequisites
            check_env_file
            deploy_quick_update
            ;;
        "dev-reset")
            check_prerequisites
            check_env_file
            deploy_with_reset
            ;;
        "prod-reset")
            check_prerequisites
            check_env_file
            upgrade_prod_with_reset
            ;;
        "reset-db")
            check_prerequisites
            check_env_file
            reset_database
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs
            ;;
        "stop")
            stop_deployment
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Run main function with all arguments
main "$@"
