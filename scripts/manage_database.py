#!/usr/bin/env python3
"""Database management script for ManagerPort RAG Chatbot."""

import sys
import argparse
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from rag_chatbot.config import Config
from rag_chatbot.vector_store import VectorStore
from rag_chatbot.document_processor import DocumentProcessor
from rag_chatbot.utils.logger import logger


def reset_database():
    """Reset the vector database and reload all documents."""
    print("🔄 Resetting vector database...")
    
    try:
        # Check if we have an API key
        if not Config.OPENAI_API_KEY:
            print("❌ OpenAI API key not found!")
            print("   Please set OPENAI_API_KEY in your .env file")
            return False
        
        # Initialize vector store
        vector_store = VectorStore()
        
        # Force rebuild the database
        success = vector_store.force_rebuild_database()
        
        if success:
            print("✅ Database reset completed successfully!")
            
            # Show summary
            stats = vector_store.get_collection_stats()
            print(f"📊 Database now contains {stats.get('total_documents', 0)} documents")
            
            return True
        else:
            print("❌ Database reset failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error during database reset: {e}")
        logger.error(f"Database reset failed: {e}")
        return False


def show_database_status():
    """Show current database status."""
    print("📊 Checking database status...")
    
    try:
        # Check if we have an API key
        if not Config.OPENAI_API_KEY:
            print("❌ OpenAI API key not found!")
            print("   Please set OPENAI_API_KEY in your .env file")
            return
        
        # Initialize vector store
        vector_store = VectorStore()
        collection = vector_store.get_or_create_collection()
        
        # Get statistics
        stats = vector_store.get_collection_stats()
        
        print("✅ Database Status:")
        print(f"   - Collection name: {Config.COLLECTION_NAME}")
        print(f"   - Document count: {stats.get('total_documents', 0)}")
        print(f"   - Database path: {Config.VECTOR_DB_PATH}")

        if stats.get('total_documents', 0) > 0:
            print(f"   - Unique source files: {stats.get('unique_source_files', 0)}")
            print(f"   - Average text length: {stats.get('average_text_length', 0):.0f} chars")
            print(f"   - Embedding model: {stats.get('embedding_model', 'N/A')}")
        
        # Check available source files
        pdf_files = Config.get_pdf_files()
        md_files = Config.get_md_files()
        
        print(f"\n📁 Available source files:")
        print(f"   - PDF files: {len(pdf_files)}")
        print(f"   - Markdown files: {len(md_files)}")
        print(f"   - Total files: {len(pdf_files) + len(md_files)}")
        
        if pdf_files:
            print(f"\n📄 PDF files:")
            for pdf_file in pdf_files:
                print(f"   - {pdf_file.name}")
        
        if md_files:
            print(f"\n📝 Markdown files:")
            for md_file in md_files:
                print(f"   - {md_file.name}")
        
    except Exception as e:
        print(f"❌ Error checking database status: {e}")
        logger.error(f"Database status check failed: {e}")


def process_documents_only():
    """Process documents without updating the database."""
    print("📄 Processing documents...")
    
    try:
        # Process all documents
        processor = DocumentProcessor()
        documents = processor.process_all_documents()
        
        if documents:
            # Save processed texts
            output_paths = processor.save_processed_texts()
            
            # Show summary
            summary = processor.get_processing_summary()
            
            print("✅ Document processing completed!")
            print(f"📊 Processing Summary:")
            print(f"   - Total documents: {summary['total_documents']}")
            print(f"   - PDF documents: {summary['pdf_documents']}")
            print(f"   - Markdown documents: {summary['markdown_documents']}")
            print(f"   - Total characters: {summary['total_characters']:,}")
            
            print(f"\n💾 Processed texts saved to:")
            for doc_type, path in output_paths.items():
                print(f"   - {doc_type.upper()}: {path}")
            
            return True
        else:
            print("❌ No documents were processed!")
            return False
            
    except Exception as e:
        print(f"❌ Error processing documents: {e}")
        logger.error(f"Document processing failed: {e}")
        return False


def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Database management script for ManagerPort RAG Chatbot",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python scripts/manage_database.py status          # Show database status
  python scripts/manage_database.py reset           # Reset database and reload all documents
  python scripts/manage_database.py process         # Process documents only (no database update)
        """
    )
    
    parser.add_argument(
        'command',
        choices=['status', 'reset', 'process'],
        help='Command to execute'
    )
    
    args = parser.parse_args()
    
    print("🚀 ManagerPort RAG Chatbot - Database Management")
    print("=" * 50)
    
    if args.command == 'status':
        show_database_status()
    elif args.command == 'reset':
        success = reset_database()
        sys.exit(0 if success else 1)
    elif args.command == 'process':
        success = process_documents_only()
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
