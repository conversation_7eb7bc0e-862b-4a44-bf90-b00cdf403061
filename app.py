"""Main application entry point for the ManagerPort RAG Chatbot."""

import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from rag_chatbot.gradio_interface import launch_chat_app
from rag_chatbot.config import Config
from rag_chatbot.utils.logger import logger


def main():
    """Launch the ManagerPort RAG Chatbot application."""
    try:
        logger.info("🚀 Starting ManagerPort RAG Chatbot")
        
        # Validate configuration
        if not Config.OPENAI_API_KEY:
            print("❌ OpenAI API key not found!")
            print("   Please set OPENAI_API_KEY in your .env file")
            print("   Copy .env.example to .env and add your API key")
            return
        
        print("🔑 OpenAI API key found")
        print("🚀 Launching ManagerPort RAG Chatbot...")
        print(f"🌐 Server will be available at: http://{Config.GRADIO_SERVER_NAME}:{Config.GRADIO_SERVER_PORT}")
        
        # Launch the chat application
        launch_chat_app()
        
    except KeyboardInterrupt:
        logger.info("Application stopped by user")
        print("\n👋 Chatbot stopped. Goodbye!")
    except Exception as e:
        logger.error(f"Application failed to start: {e}")
        print(f"❌ Failed to start application: {e}")
        raise


if __name__ == "__main__":
    main()
