# 🐳 Docker Deployment Guide

This guide explains how to deploy the ManagerPort RAG Chatbot using Docker and Docker Compose.

## 📋 Prerequisites

- Docker Engine 20.10+ with Compose plugin
- OpenAI API key
- At least 2GB RAM
- 5GB free disk space

> **Note**: If you encounter dependency conflicts during Docker build, see [DOCKER_DEPLOYMENT_FIX.md](DOCKER_DEPLOYMENT_FIX.md) for the resolution.

## 🚀 Quick Start

### 1. <PERSON><PERSON> and Setup

```bash
git clone <repository-url>
cd managerport-chatbot
```

### 2. Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit .env and add your OpenAI API key
nano .env
```

**Required configuration:**

```env
OPENAI_API_KEY=your_actual_api_key_here
```

### 3. Deploy

#### Development Deployment

```bash
# Using deployment script (recommended)
./scripts/deploy.sh dev

# Or manually
docker compose up -d
```

#### Production Deployment

```bash
# Using deployment script (recommended)
./scripts/deploy.sh prod

# Or manually
docker compose -f docker-compose.prod.yml up -d
```

## 🔧 Deployment Options

### Development Environment

**Features:**

- Direct port exposure (7860)
- Development-friendly logging
- Hot reload capabilities
- Easy debugging

**Access:**

- Chatbot: http://localhost:7860

### Production Environment

**Features:**

- Nginx Proxy Manager integration
- SSL/TLS termination
- Domain-based routing
- Enhanced security
- Automatic restarts

**Access:**

- Nginx Proxy Manager UI: http://localhost:81
- Default credentials: `<EMAIL>` / `changeme`

### Production with Monitoring

**Additional features:**

- Uptime Kuma monitoring
- Health checks
- Performance metrics

**Access:**

- Uptime Kuma: http://localhost:3001

## 🛠️ Management Commands

### Using Deployment Script

```bash
# Deploy development environment
./scripts/deploy.sh dev

# Deploy production environment
./scripts/deploy.sh prod

# Deploy with monitoring
./scripts/deploy.sh monitoring

# Check deployment status
./scripts/deploy.sh status

# View logs
./scripts/deploy.sh logs

# Stop all deployments
./scripts/deploy.sh stop

# Show help
./scripts/deploy.sh help
```

### Manual Docker Compose Commands

```bash
# Start services
docker compose up -d

# Stop services
docker compose down

# View logs
docker compose logs -f managerport-chatbot

# Rebuild and restart
docker compose down
docker compose build --no-cache
docker compose up -d

# Check status
docker compose ps

# Execute commands in container
docker compose exec managerport-chatbot bash
```

## 🌐 Nginx Proxy Manager Setup

### Initial Setup

1. Access Nginx Proxy Manager UI at http://localhost:81
2. Login with default credentials:
   - Email: `<EMAIL>`
   - Password: `changeme`
3. **Immediately change the default password!**

### Configure Proxy Host

1. Go to "Proxy Hosts" → "Add Proxy Host"
2. Configure:
   - **Domain Names:** `chatbot.yourdomain.com`
   - **Scheme:** `http`
   - **Forward Hostname/IP:** `managerport-chatbot-prod`
   - **Forward Port:** `7860`
3. Enable SSL:
   - Go to "SSL" tab
   - Select "Request a new SSL Certificate"
   - Enable "Force SSL"
   - Add your email for Let's Encrypt

## 🔒 Security Considerations

### Production Security

1. **Change default passwords immediately**
2. **Use strong OpenAI API key**
3. **Configure firewall rules**
4. **Enable SSL/TLS**
5. **Regular updates**

### Environment Variables

```env
# Security
GRADIO_SHARE=false  # Never enable in production

# Logging
LOG_LEVEL=INFO      # Use INFO or WARNING in production

# Model selection
OPENAI_MODEL=gpt-4.1-nano  # Most cost-effective option (default)
```

## 📊 Monitoring and Maintenance

### Health Checks

The application includes built-in health checks:

- HTTP endpoint: `http://localhost:7860/`
- Check interval: 30 seconds
- Timeout: 10 seconds

### Log Management

```bash
# View real-time logs
docker compose logs -f

# View specific service logs
docker compose logs -f managerport-chatbot

# Limit log output
docker compose logs --tail=100 managerport-chatbot
```

### Data Persistence

Persistent volumes:

- `chatbot_data`: Vector database and application data
- `chatbot_logs`: Application logs
- `npm_data`: Nginx Proxy Manager configuration
- `npm_letsencrypt`: SSL certificates

### Backup

```bash
# Backup volumes
docker run --rm -v chatbot_data:/data -v $(pwd):/backup alpine tar czf /backup/chatbot_data_backup.tar.gz -C /data .

# Restore volumes
docker run --rm -v chatbot_data:/data -v $(pwd):/backup alpine tar xzf /backup/chatbot_data_backup.tar.gz -C /data
```

## 🔄 Updates and Maintenance

### Automatic Updates (Optional)

Enable Watchtower for automatic container updates:

```bash
docker compose -f docker-compose.prod.yml --profile autoupdate up -d
```

### Manual Updates

```bash
# Pull latest images
docker compose pull

# Rebuild and restart
docker compose down
docker compose build --no-cache
docker compose up -d
```

## 🐛 Troubleshooting

### Common Issues

1. **Port already in use**

   ```bash
   # Check what's using the port
   lsof -i :7860

   # Change port in docker-compose.yml
   ports:
     - "8080:7860"  # Use different external port
   ```

2. **OpenAI API errors**

   - Check API key in `.env`
   - Verify API quota
   - Check network connectivity

3. **Container won't start**

   ```bash
   # Check logs
   docker compose logs managerport-chatbot

   # Check container status
   docker compose ps
   ```

4. **Permission issues**
   ```bash
   # Fix ownership
   sudo chown -R $USER:$USER data/ logs/
   ```

### Performance Tuning

```env
# Optimize for your hardware
CHUNK_SIZE=1500          # Larger chunks for better context
RETRIEVAL_K=3            # Fewer results for faster response
OPENAI_MODEL=gpt-4o-mini # Faster, cheaper model
```

## 📞 Support

For issues and questions:

1. Check logs: `docker compose logs -f`
2. Verify configuration: `./scripts/deploy.sh status`
3. Review this documentation
4. Check GitHub issues

## 🎯 Next Steps

After successful deployment:

1. Configure domain and SSL
2. Set up monitoring
3. Configure backups
4. Test functionality
5. Train users
