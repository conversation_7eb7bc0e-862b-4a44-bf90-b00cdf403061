"""Test script to validate Docker configuration files."""

import os
import yaml
from pathlib import Path


def test_dockerfile():
    """Test Dockerfile exists and has basic structure."""
    print("🐳 Testing Dockerfile...")
    
    dockerfile_path = Path("Dockerfile")
    if not dockerfile_path.exists():
        print("❌ Dockerfile not found")
        return False
    
    content = dockerfile_path.read_text()
    
    # Check for essential components
    required_elements = [
        "FROM python:",
        "WORKDIR /app",
        "COPY requirements.txt",
        "RUN pip install",
        "COPY . .",
        "EXPOSE 7860",
        "CMD"
    ]
    
    missing = []
    for element in required_elements:
        if element not in content:
            missing.append(element)
    
    if missing:
        print(f"❌ Missing elements in Dockerfile: {missing}")
        return False
    
    print("✅ Dockerfile structure is valid")
    return True


def test_docker_compose():
    """Test docker-compose.yml files."""
    print("\n🐳 Testing Docker Compose files...")
    
    compose_files = ["docker-compose.yml", "docker-compose.prod.yml"]
    
    for compose_file in compose_files:
        print(f"\n  Testing {compose_file}...")
        
        if not Path(compose_file).exists():
            print(f"❌ {compose_file} not found")
            return False
        
        try:
            with open(compose_file, 'r') as f:
                config = yaml.safe_load(f)
            
            # Check basic structure
            if 'services' not in config:
                print(f"❌ No 'services' section in {compose_file}")
                return False
            
            if 'managerport-chatbot' not in config['services'] and 'managerport-chatbot-prod' not in config['services']:
                print(f"❌ No chatbot service found in {compose_file}")
                return False
            
            # Check for required environment variables
            service_name = 'managerport-chatbot' if 'managerport-chatbot' in config['services'] else 'managerport-chatbot-prod'
            service = config['services'][service_name]
            
            if 'environment' not in service:
                print(f"❌ No environment section in {compose_file}")
                return False
            
            env_vars = service['environment']
            required_env = ['OPENAI_API_KEY', 'GRADIO_SERVER_NAME', 'GRADIO_SERVER_PORT']
            
            for var in required_env:
                found = any(var in str(env_item) for env_item in env_vars)
                if not found:
                    print(f"❌ Missing environment variable {var} in {compose_file}")
                    return False
            
            print(f"✅ {compose_file} structure is valid")
            
        except yaml.YAMLError as e:
            print(f"❌ YAML syntax error in {compose_file}: {e}")
            return False
        except Exception as e:
            print(f"❌ Error reading {compose_file}: {e}")
            return False
    
    return True


def test_dockerignore():
    """Test .dockerignore file."""
    print("\n🐳 Testing .dockerignore...")
    
    dockerignore_path = Path(".dockerignore")
    if not dockerignore_path.exists():
        print("❌ .dockerignore not found")
        return False
    
    content = dockerignore_path.read_text()
    
    # Check for important exclusions
    important_exclusions = [
        "__pycache__",
        "*.pyc",
        ".git",
        "venv/",
        ".env",
        "data/",
        "logs/"
    ]
    
    missing = []
    for exclusion in important_exclusions:
        if exclusion not in content:
            missing.append(exclusion)
    
    if missing:
        print(f"⚠️ Recommended exclusions missing from .dockerignore: {missing}")
    
    print("✅ .dockerignore exists and has basic exclusions")
    return True


def test_deployment_script():
    """Test deployment script."""
    print("\n🐳 Testing deployment script...")
    
    script_path = Path("scripts/deploy.sh")
    if not script_path.exists():
        print("❌ scripts/deploy.sh not found")
        return False
    
    # Check if script is executable
    if not os.access(script_path, os.X_OK):
        print("❌ scripts/deploy.sh is not executable")
        return False
    
    content = script_path.read_text()
    
    # Check for essential functions
    required_functions = [
        "deploy_dev()",
        "deploy_prod()",
        "check_prerequisites()",
        "check_env_file()"
    ]
    
    missing = []
    for func in required_functions:
        if func not in content:
            missing.append(func)
    
    if missing:
        print(f"❌ Missing functions in deploy.sh: {missing}")
        return False
    
    print("✅ Deployment script structure is valid")
    return True


def test_env_example():
    """Test .env.example file."""
    print("\n🐳 Testing .env.example...")
    
    env_example_path = Path(".env.example")
    if not env_example_path.exists():
        print("❌ .env.example not found")
        return False
    
    content = env_example_path.read_text()
    
    # Check for required variables
    required_vars = [
        "OPENAI_API_KEY",
        "OPENAI_MODEL",
        "EMBEDDING_MODEL",
        "GRADIO_SERVER_NAME",
        "GRADIO_SERVER_PORT"
    ]
    
    missing = []
    for var in required_vars:
        if var not in content:
            missing.append(var)
    
    if missing:
        print(f"❌ Missing variables in .env.example: {missing}")
        return False
    
    # Check that API key is placeholder
    if "your_openai_api_key_here" not in content:
        print("⚠️ .env.example should contain placeholder API key")
    
    print("✅ .env.example contains required variables")
    return True


def main():
    """Run all Docker configuration tests."""
    print("🧪 Docker Configuration Validation")
    print("=" * 50)
    
    tests = [
        ("Dockerfile", test_dockerfile),
        ("Docker Compose", test_docker_compose),
        ("Docker Ignore", test_dockerignore),
        ("Deployment Script", test_deployment_script),
        ("Environment Example", test_env_example),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Docker configuration tests passed!")
        print("\n🚀 Ready for Docker deployment!")
        print("\nNext steps:")
        print("1. Install Docker and Docker Compose")
        print("2. Configure .env file with your OpenAI API key")
        print("3. Run: ./scripts/deploy.sh dev")
    else:
        print("⚠️ Some tests failed. Please fix the issues above.")
    
    return passed == total


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
