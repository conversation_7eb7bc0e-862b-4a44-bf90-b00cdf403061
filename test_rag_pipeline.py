"""Test script for RAG pipeline functionality."""

import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from rag_chatbot.rag_pipeline import RAGPipeline
from rag_chatbot.config import Config
from rag_chatbot.utils.logger import logger


def main():
    """Test RAG pipeline functionality."""
    try:
        logger.info("🧪 Testing RAG Pipeline")
        
        # Check if we have an API key
        if not Config.OPENAI_API_KEY:
            print("❌ OpenAI API key not found. Please set OPENAI_API_KEY in .env file")
            print("   Copy .env.example to .env and add your API key")
            return
        
        print("🔑 OpenAI API key found")
        
        # Initialize RAG pipeline
        print("\n🤖 Initializing RAG pipeline...")
        rag = RAGPipeline()
        
        # Get pipeline info
        info = rag.get_pipeline_info()
        print(f"✅ RAG pipeline ready!")
        print(f"📊 Model: {info['model']}")
        print(f"📊 Vector DB: {info['vector_database']['total_documents']} documents from {info['vector_database']['unique_files']} files")
        print(f"📊 Retrieval K: {info['retrieval_k']}")
        
        # Test questions in Slovak
        test_questions = [
            "Aké sú najčastejšie problémy v projektovom manažmente?",
            "Ako riešiť konflikty v tíme?",
            "Čo je dôležité pri vedení ľudí?",
            "Aké sú trendy v manažmente?",
            "Ako motivovať zamestnancov?"
        ]
        
        print(f"\n🔍 Testing with {len(test_questions)} questions...")
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n{'='*80}")
            print(f"🔎 Otázka {i}: {question}")
            print("="*80)
            
            # Ask question
            response = rag.ask_question(question)
            
            # Display answer
            print(f"\n💬 Odpoveď:")
            print(response['answer'])
            
            # Display sources
            if response['sources']:
                print(f"\n📚 Zdroje ({len(response['sources'])}):")
                for j, source in enumerate(response['sources'], 1):
                    print(f"  {j}. {source['file']} ({source['year']}) - Podobnosť: {source['similarity']:.3f}")
            
            print(f"\n📊 Načítané dokumenty: {response['retrieved_documents']}")
            
            if 'error' in response:
                print(f"⚠️ Chyba: {response['error']}")
        
        # Test batch processing
        print(f"\n{'='*80}")
        print("🔄 Testing batch processing...")
        print("="*80)
        
        batch_questions = [
            "Čo je agile metodika?",
            "Ako robiť efektívne porady?"
        ]
        
        batch_responses = rag.batch_questions(batch_questions)
        
        for i, response in enumerate(batch_responses, 1):
            print(f"\n📝 Batch otázka {i}: {response['question']}")
            print(f"💬 Odpoveď: {response['answer'][:200]}...")
            print(f"📚 Zdroje: {len(response['sources'])}")
        
        print(f"\n✅ RAG pipeline test completed successfully!")
        print(f"🎯 Ready for Gradio interface integration")
        
    except Exception as e:
        logger.error(f"❌ RAG pipeline test failed: {e}")
        raise


if __name__ == "__main__":
    main()
