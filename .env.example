# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Model Configuration
OPENAI_MODEL=gpt-4.1-nano
EMBEDDING_MODEL=text-embedding-ada-002
LLM_MODEL=gpt-4-turbo-preview
TEMPERATURE=0.1

# Vector Database Configuration
VECTOR_DB_PATH=./data/chroma_db
COLLECTION_NAME=managerport_docs

# Text Processing Configuration
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# RAG Configuration
RETRIEVAL_K=5

# Gradio Configuration
GRADIO_SERVER_NAME=0.0.0.0
GRADIO_SERVER_PORT=7860
GRADIO_SHARE=false

# Logging
LOG_LEVEL=INFO
