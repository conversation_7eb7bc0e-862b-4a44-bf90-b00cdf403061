"""Configuration validation utilities for the RAG chatbot."""

import os
import re
from pathlib import Path
from typing import Dict, List, Tuple, Any

from .config import Config
from .utils.logger import logger


class ConfigValidator:
    """Validates configuration settings and environment setup."""
    
    def __init__(self):
        """Initialize the configuration validator."""
        self.errors = []
        self.warnings = []
        self.info = []
    
    def validate_all(self) -> Tuple[bool, Dict[str, Any]]:
        """
        Validate all configuration settings.
        
        Returns:
            Tuple of (is_valid, validation_report)
        """
        self.errors.clear()
        self.warnings.clear()
        self.info.clear()
        
        # Run all validation checks
        self._validate_openai_config()
        self._validate_paths()
        self._validate_model_config()
        self._validate_vector_db_config()
        self._validate_gradio_config()
        self._validate_environment()
        
        is_valid = len(self.errors) == 0
        
        report = {
            "valid": is_valid,
            "errors": self.errors,
            "warnings": self.warnings,
            "info": self.info,
            "summary": self._generate_summary()
        }
        
        return is_valid, report
    
    def _validate_openai_config(self):
        """Validate OpenAI configuration."""
        # Check API key
        if not Config.OPENAI_API_KEY:
            self.errors.append("OPENAI_API_KEY is not set")
        elif Config.OPENAI_API_KEY == "your_openai_api_key_here":
            self.errors.append("OPENAI_API_KEY is still set to placeholder value")
        elif not self._is_valid_openai_key(Config.OPENAI_API_KEY):
            self.errors.append("OPENAI_API_KEY format appears invalid")
        else:
            self.info.append(f"OpenAI API key configured (length: {len(Config.OPENAI_API_KEY)})")
        
        # Check models
        valid_models = ["gpt-4", "gpt-4-turbo", "gpt-4-turbo-preview", "gpt-3.5-turbo"]
        if Config.OPENAI_MODEL not in valid_models:
            self.warnings.append(f"OPENAI_MODEL '{Config.OPENAI_MODEL}' may not be supported")
        else:
            self.info.append(f"OpenAI model: {Config.OPENAI_MODEL}")
        
        # Check embedding model
        valid_embedding_models = ["text-embedding-ada-002", "text-embedding-3-small", "text-embedding-3-large"]
        if Config.EMBEDDING_MODEL not in valid_embedding_models:
            self.warnings.append(f"EMBEDDING_MODEL '{Config.EMBEDDING_MODEL}' may not be supported")
        else:
            self.info.append(f"Embedding model: {Config.EMBEDDING_MODEL}")
        
        # Check temperature
        if not 0.0 <= Config.TEMPERATURE <= 2.0:
            self.warnings.append(f"TEMPERATURE {Config.TEMPERATURE} is outside recommended range (0.0-2.0)")
        else:
            self.info.append(f"Temperature: {Config.TEMPERATURE}")
    
    def _validate_paths(self):
        """Validate file paths and directories."""
        # Check project root
        if not Config.PROJECT_ROOT.exists():
            self.errors.append(f"Project root directory not found: {Config.PROJECT_ROOT}")
        else:
            self.info.append(f"Project root: {Config.PROJECT_ROOT}")
        
        # Check PDF directory
        if not Config.PDF_DIR.exists():
            self.errors.append(f"PDF directory not found: {Config.PDF_DIR}")
        else:
            pdf_files = Config.get_pdf_files()
            if len(pdf_files) == 0:
                self.warnings.append("No PDF files found in PDF directory")
            else:
                self.info.append(f"Found {len(pdf_files)} PDF files")
        
        # Check data directory
        if not Config.DATA_DIR.exists():
            self.warnings.append(f"Data directory will be created: {Config.DATA_DIR}")
        else:
            self.info.append(f"Data directory: {Config.DATA_DIR}")
        
        # Check logs directory
        if not Config.LOGS_DIR.exists():
            self.warnings.append(f"Logs directory will be created: {Config.LOGS_DIR}")
        else:
            self.info.append(f"Logs directory: {Config.LOGS_DIR}")
    
    def _validate_model_config(self):
        """Validate model configuration parameters."""
        # Check chunk size
        if Config.CHUNK_SIZE < 100:
            self.warnings.append(f"CHUNK_SIZE {Config.CHUNK_SIZE} is very small")
        elif Config.CHUNK_SIZE > 4000:
            self.warnings.append(f"CHUNK_SIZE {Config.CHUNK_SIZE} is very large")
        else:
            self.info.append(f"Chunk size: {Config.CHUNK_SIZE}")
        
        # Check chunk overlap
        if Config.CHUNK_OVERLAP >= Config.CHUNK_SIZE:
            self.errors.append("CHUNK_OVERLAP must be less than CHUNK_SIZE")
        elif Config.CHUNK_OVERLAP < 0:
            self.errors.append("CHUNK_OVERLAP must be non-negative")
        else:
            overlap_ratio = Config.CHUNK_OVERLAP / Config.CHUNK_SIZE
            if overlap_ratio > 0.5:
                self.warnings.append(f"CHUNK_OVERLAP is {overlap_ratio:.1%} of chunk size (high)")
            self.info.append(f"Chunk overlap: {Config.CHUNK_OVERLAP} ({overlap_ratio:.1%})")
        
        # Check retrieval K
        if Config.RETRIEVAL_K < 1:
            self.errors.append("RETRIEVAL_K must be at least 1")
        elif Config.RETRIEVAL_K > 20:
            self.warnings.append(f"RETRIEVAL_K {Config.RETRIEVAL_K} is very high")
        else:
            self.info.append(f"Retrieval K: {Config.RETRIEVAL_K}")
    
    def _validate_vector_db_config(self):
        """Validate vector database configuration."""
        # Check vector DB path
        if not Config.VECTOR_DB_PATH.parent.exists():
            self.warnings.append(f"Vector DB parent directory will be created: {Config.VECTOR_DB_PATH.parent}")
        
        # Check collection name
        if not Config.COLLECTION_NAME:
            self.errors.append("COLLECTION_NAME cannot be empty")
        elif not re.match(r'^[a-zA-Z0-9_-]+$', Config.COLLECTION_NAME):
            self.warnings.append("COLLECTION_NAME contains special characters")
        else:
            self.info.append(f"Collection name: {Config.COLLECTION_NAME}")
    
    def _validate_gradio_config(self):
        """Validate Gradio configuration."""
        # Check server name
        if Config.GRADIO_SERVER_NAME not in ["0.0.0.0", "127.0.0.1", "localhost"]:
            self.warnings.append(f"GRADIO_SERVER_NAME '{Config.GRADIO_SERVER_NAME}' may not be accessible")
        else:
            self.info.append(f"Gradio server: {Config.GRADIO_SERVER_NAME}:{Config.GRADIO_SERVER_PORT}")
        
        # Check port
        if not 1024 <= Config.GRADIO_SERVER_PORT <= 65535:
            self.warnings.append(f"GRADIO_SERVER_PORT {Config.GRADIO_SERVER_PORT} is outside typical range")
        
        # Check share setting
        if Config.GRADIO_SHARE:
            self.warnings.append("GRADIO_SHARE is enabled - interface will be publicly accessible")
        else:
            self.info.append("Gradio sharing disabled (local access only)")
    
    def _validate_environment(self):
        """Validate environment and dependencies."""
        # Check Python version
        import sys
        python_version = f"{sys.version_info.major}.{sys.version_info.minor}"
        if sys.version_info < (3, 8):
            self.errors.append(f"Python {python_version} is too old (requires 3.8+)")
        else:
            self.info.append(f"Python version: {python_version}")
        
        # Check log level
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if Config.LOG_LEVEL not in valid_log_levels:
            self.warnings.append(f"LOG_LEVEL '{Config.LOG_LEVEL}' is not standard")
        else:
            self.info.append(f"Log level: {Config.LOG_LEVEL}")
    
    def _is_valid_openai_key(self, key: str) -> bool:
        """Check if OpenAI API key format is valid."""
        # OpenAI keys typically start with 'sk-' and are 51+ characters
        return key.startswith('sk-') and len(key) >= 40
    
    def _generate_summary(self) -> str:
        """Generate a summary of validation results."""
        if self.errors:
            return f"❌ Configuration invalid: {len(self.errors)} errors, {len(self.warnings)} warnings"
        elif self.warnings:
            return f"⚠️ Configuration valid with warnings: {len(self.warnings)} warnings"
        else:
            return "✅ Configuration is valid"
    
    def print_report(self, report: Dict[str, Any]):
        """Print a formatted validation report."""
        print("🔧 Configuration Validation Report")
        print("=" * 50)
        
        print(f"\n{report['summary']}")
        
        if report['errors']:
            print(f"\n❌ Errors ({len(report['errors'])}):")
            for error in report['errors']:
                print(f"  • {error}")
        
        if report['warnings']:
            print(f"\n⚠️ Warnings ({len(report['warnings'])}):")
            for warning in report['warnings']:
                print(f"  • {warning}")
        
        if report['info']:
            print(f"\nℹ️ Configuration Info ({len(report['info'])}):")
            for info in report['info']:
                print(f"  • {info}")


def validate_config() -> bool:
    """
    Validate configuration and print report.
    
    Returns:
        True if configuration is valid, False otherwise
    """
    validator = ConfigValidator()
    is_valid, report = validator.validate_all()
    validator.print_report(report)
    return is_valid


if __name__ == "__main__":
    import sys
    is_valid = validate_config()
    sys.exit(0 if is_valid else 1)
