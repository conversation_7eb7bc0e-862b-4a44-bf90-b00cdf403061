"""Logging configuration for the RAG chatbot."""

import sys
from pathlib import Path
from loguru import logger

from ..config import Config


def setup_logger():
    """Set up the logger with appropriate configuration."""
    # Remove default handler
    logger.remove()
    
    # Add console handler
    logger.add(
        sys.stdout,
        level=Config.LOG_LEVEL,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # Add file handler
    log_file = Config.LOGS_DIR / "rag_chatbot.log"
    logger.add(
        log_file,
        level=Config.LOG_LEVEL,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="7 days"
    )
    
    return logger


# Initialize logger
setup_logger()
