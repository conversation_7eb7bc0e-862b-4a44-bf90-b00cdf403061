"""RAG (Retrieval-Augmented Generation) pipeline implementation."""

from typing import List, Dict, Any, Optional
from langchain.schema import Document
from langchain.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from langchain.schema.runnable import RunnablePassthrough
from langchain.schema.output_parser import StrOutputParser

from .config import Config
from .vector_store import VectorStore
from .utils.logger import logger


class RAGPipeline:
    """RAG pipeline for question answering using ManagerPort documents."""
    
    def __init__(self):
        """Initialize the RAG pipeline."""
        self.vector_store = None
        self.llm = None
        self.prompt_template = None
        self.chain = None
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize all RAG components."""
        try:
            logger.info("Initializing RAG pipeline components...")
            
            # Initialize vector store
            logger.info("Setting up vector store...")
            self.vector_store = VectorStore()
            
            # Initialize LLM
            logger.info(f"Initializing OpenAI LLM: {Config.OPENAI_MODEL}")
            self.llm = ChatOpenAI(
                model=Config.OPENAI_MODEL,
                temperature=Config.TEMPERATURE,
                api_key=Config.OPENAI_API_KEY
            )
            
            # Create prompt template
            self._create_prompt_template()
            
            # Build the RAG chain
            self._build_rag_chain()
            
            logger.info("✅ RAG pipeline initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize RAG pipeline: {e}")
            raise
    
    def _create_prompt_template(self):
        """Create the prompt template for RAG."""
        template = """Ste odborný asistent pre ManagerPort komunitu, ktorá sa zaoberá manažmentom a projektovým riadením.

Vaša úloha je odpovedať na otázky na základe poskytnutého kontextu z dokumentov ManagerPort komunity.

KONTEXT:
{context}

OTÁZKA: {question}

POKYNY:
1. Odpovedajte výlučne na základe poskytnutého kontextu
2. Ak informácia nie je v kontexte, povedzte "Na základe dostupných dokumentov nemôžem nájsť odpoveď na túto otázku"
3. Používajte slovenský jazyk
4. Buďte konkrétni a praktickí
5. Ak je to relevantné, uveďte z ktorého dokumentu/roku pochádza informácia
6. Formátujte odpoveď prehľadne s odrážkami alebo číslovaním ak je to vhodné

ODPOVEĎ:"""

        self.prompt_template = ChatPromptTemplate.from_template(template)
        logger.info("✅ Prompt template created")
    
    def _build_rag_chain(self):
        """Build the RAG chain using LangChain."""
        def format_docs(docs):
            """Format retrieved documents for the prompt."""
            formatted = []
            for doc in docs:
                source = doc.get('metadata', {}).get('source_file', 'Neznámy zdroj')
                year = self._extract_year_from_filename(source)
                text = doc.get('text', '')
                formatted.append(f"[{source} - {year}]\n{text}")
            return "\n\n".join(formatted)
        
        def retrieve_documents(query: str) -> List[Dict]:
            """Retrieve relevant documents for the query."""
            try:
                results = self.vector_store.search_similar_documents(
                    query, 
                    n_results=Config.RETRIEVAL_K
                )
                logger.info(f"Retrieved {len(results)} documents for query")
                return results
            except Exception as e:
                logger.error(f"Document retrieval failed: {e}")
                return []
        
        # Build the chain
        self.chain = (
            {
                "context": lambda x: format_docs(retrieve_documents(x["question"])),
                "question": RunnablePassthrough()
            }
            | self.prompt_template
            | self.llm
            | StrOutputParser()
        )
        
        logger.info("✅ RAG chain built successfully")
    
    def _extract_year_from_filename(self, filename: str) -> str:
        """Extract year from filename."""
        try:
            # Look for 4-digit year in filename
            import re
            year_match = re.search(r'(20\d{2})', filename)
            if year_match:
                return year_match.group(1)
            return "Neznámy rok"
        except:
            return "Neznámy rok"
    
    def ask_question(self, question: str) -> Dict[str, Any]:
        """
        Ask a question and get an answer using RAG.
        
        Args:
            question: The question to ask
            
        Returns:
            Dictionary containing answer, sources, and metadata
        """
        try:
            logger.info(f"Processing question: {question[:100]}...")
            
            # Retrieve relevant documents
            retrieved_docs = self.vector_store.search_similar_documents(
                question, 
                n_results=Config.RETRIEVAL_K
            )
            
            # Generate answer using the chain
            answer = self.chain.invoke({"question": question})
            
            # Prepare response
            response = {
                "question": question,
                "answer": answer,
                "sources": self._format_sources(retrieved_docs),
                "retrieved_documents": len(retrieved_docs),
                "model": Config.OPENAI_MODEL
            }
            
            logger.info("✅ Question processed successfully")
            return response
            
        except Exception as e:
            logger.error(f"Failed to process question: {e}")
            return {
                "question": question,
                "answer": f"Ospravedlňujem sa, ale nastala chyba pri spracovaní vašej otázky: {str(e)}",
                "sources": [],
                "retrieved_documents": 0,
                "model": Config.OPENAI_MODEL,
                "error": str(e)
            }
    
    def _format_sources(self, documents: List[Dict]) -> List[Dict]:
        """Format source information from retrieved documents."""
        sources = []
        seen_sources = set()
        
        for doc in documents:
            metadata = doc.get('metadata', {})
            source_file = metadata.get('source_file', 'Neznámy zdroj')
            
            if source_file not in seen_sources:
                sources.append({
                    "file": source_file,
                    "year": self._extract_year_from_filename(source_file),
                    "similarity": doc.get('similarity_score', 0.0),
                    "chunk_index": metadata.get('chunk_index', 0)
                })
                seen_sources.add(source_file)
        
        return sources
    
    def get_pipeline_info(self) -> Dict[str, Any]:
        """Get information about the RAG pipeline configuration."""
        try:
            collection_stats = self.vector_store.get_collection_stats()
            return {
                "model": Config.OPENAI_MODEL,
                "temperature": Config.TEMPERATURE,
                "retrieval_k": Config.RETRIEVAL_K,
                "embedding_model": Config.EMBEDDING_MODEL,
                "vector_database": {
                    "total_documents": collection_stats.get('total_documents', 0),
                    "unique_files": collection_stats.get('unique_source_files', 0),
                    "collection_name": collection_stats.get('collection_name', 'N/A')
                },
                "status": "ready"
            }
        except Exception as e:
            logger.error(f"Failed to get pipeline info: {e}")
            return {"status": "error", "error": str(e)}
    
    def batch_questions(self, questions: List[str]) -> List[Dict[str, Any]]:
        """
        Process multiple questions in batch.
        
        Args:
            questions: List of questions to process
            
        Returns:
            List of response dictionaries
        """
        logger.info(f"Processing batch of {len(questions)} questions...")
        responses = []
        
        for i, question in enumerate(questions, 1):
            logger.info(f"Processing question {i}/{len(questions)}")
            response = self.ask_question(question)
            responses.append(response)
        
        logger.info("✅ Batch processing completed")
        return responses
