"""Vector database implementation using Chroma for document storage and retrieval."""

import uuid
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import chromadb
from chromadb.config import Settings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_openai import OpenAIEmbeddings
from loguru import logger

from .config import Config


class VectorStore:
    """Manages vector storage and retrieval using Chroma database."""
    
    def __init__(self):
        """Initialize the vector store."""
        self.embeddings = None
        self.client = None
        self.collection = None
        self.text_splitter = None
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize embeddings, client, and text splitter."""
        try:
            # Initialize OpenAI embeddings
            logger.info("Initializing OpenAI embeddings...")
            self.embeddings = OpenAIEmbeddings(
                openai_api_key=Config.OPENAI_API_KEY,
                model=Config.EMBEDDING_MODEL
            )
            
            # Initialize Chroma client
            logger.info(f"Initializing Chroma client at {Config.VECTOR_DB_PATH}")
            Config.VECTOR_DB_PATH.mkdir(parents=True, exist_ok=True)
            
            self.client = chromadb.PersistentClient(
                path=str(Config.VECTOR_DB_PATH),
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # Initialize text splitter for chunking
            logger.info(f"Initializing text splitter (chunk_size={Config.CHUNK_SIZE}, overlap={Config.CHUNK_OVERLAP})")
            self.text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=Config.CHUNK_SIZE,
                chunk_overlap=Config.CHUNK_OVERLAP,
                length_function=len,
                separators=["\n\n", "\n", ". ", " ", ""]
            )
            
            logger.info("✅ Vector store components initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize vector store components: {e}")
            raise
    
    def get_or_create_collection(self) -> chromadb.Collection:
        """Get existing collection or create a new one."""
        try:
            # Try to get existing collection
            collection = self.client.get_collection(name=Config.COLLECTION_NAME)
            logger.info(f"Found existing collection '{Config.COLLECTION_NAME}' with {collection.count()} documents")
            
        except Exception:
            # Create new collection if it doesn't exist
            logger.info(f"Creating new collection '{Config.COLLECTION_NAME}'")
            collection = self.client.create_collection(
                name=Config.COLLECTION_NAME,
                metadata={"description": "ManagerPort community documents for RAG chatbot"}
            )
        
        self.collection = collection
        return collection
    
    def chunk_documents(self, documents: Dict[str, Dict]) -> List[Dict]:
        """
        Split documents into chunks for embedding.
        
        Args:
            documents: Dictionary of processed documents
            
        Returns:
            List of document chunks with metadata
        """
        logger.info(f"Chunking {len(documents)} documents...")
        
        chunks = []
        total_chunks = 0
        
        for filename, doc_data in documents.items():
            text = doc_data['cleaned_text']
            metadata = doc_data['metadata']
            
            # Split text into chunks
            text_chunks = self.text_splitter.split_text(text)
            
            logger.info(f"Document '{filename}': {len(text_chunks)} chunks created")
            
            # Create chunk objects with metadata
            for i, chunk_text in enumerate(text_chunks):
                chunk_id = f"{filename}_{i}"
                
                chunk = {
                    "id": chunk_id,
                    "text": chunk_text,
                    "metadata": {
                        "source_file": filename,
                        "chunk_index": i,
                        "total_chunks": len(text_chunks),
                        "char_count": len(chunk_text),
                        "original_pages": metadata.get('pages_with_text', 0),
                        "file_size": metadata.get('file_size', 0),
                        "creation_date": metadata.get('creation_date', ''),
                    }
                }
                
                chunks.append(chunk)
                total_chunks += 1
        
        logger.info(f"📊 Total chunks created: {total_chunks}")
        return chunks
    
    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for a list of texts with batching to handle API limits.

        Args:
            texts: List of text strings to embed

        Returns:
            List of embedding vectors
        """
        logger.info(f"Generating embeddings for {len(texts)} texts...")

        # OpenAI has a limit of ~300k tokens per request
        # Estimate ~1000 tokens per chunk, so batch size of 200 should be safe
        batch_size = 200
        all_embeddings = []

        try:
            total_batches = (len(texts) + batch_size - 1) // batch_size

            for i in range(0, len(texts), batch_size):
                batch_end = min(i + batch_size, len(texts))
                batch_texts = texts[i:batch_end]
                batch_num = (i // batch_size) + 1

                logger.info(f"Processing embedding batch {batch_num}/{total_batches} ({len(batch_texts)} texts)...")

                batch_embeddings = self.embeddings.embed_documents(batch_texts)
                all_embeddings.extend(batch_embeddings)

                logger.info(f"✅ Batch {batch_num}/{total_batches} completed")

            logger.info(f"✅ Generated {len(all_embeddings)} embeddings total")
            return all_embeddings

        except Exception as e:
            logger.error(f"Failed to generate embeddings: {e}")
            raise
    
    def add_documents_to_collection(self, chunks: List[Dict]) -> bool:
        """
        Add document chunks to the vector collection.
        
        Args:
            chunks: List of document chunks with metadata
            
        Returns:
            True if successful, False otherwise
        """
        if not self.collection:
            self.get_or_create_collection()
        
        logger.info(f"Adding {len(chunks)} chunks to collection...")
        
        try:
            # Prepare data for batch insertion
            texts = [chunk['text'] for chunk in chunks]
            ids = [chunk['id'] for chunk in chunks]
            metadatas = [chunk['metadata'] for chunk in chunks]
            
            # Generate embeddings
            embeddings = self.generate_embeddings(texts)
            
            # Add to collection in batches to avoid memory issues
            batch_size = 100
            total_batches = (len(chunks) + batch_size - 1) // batch_size
            
            for i in range(0, len(chunks), batch_size):
                batch_end = min(i + batch_size, len(chunks))
                batch_num = (i // batch_size) + 1
                
                logger.info(f"Processing batch {batch_num}/{total_batches}...")
                
                self.collection.add(
                    embeddings=embeddings[i:batch_end],
                    documents=texts[i:batch_end],
                    metadatas=metadatas[i:batch_end],
                    ids=ids[i:batch_end]
                )
            
            logger.info(f"✅ Successfully added {len(chunks)} chunks to collection")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add documents to collection: {e}")
            return False
    
    def search_similar_documents(self, query: str, n_results: int = 5) -> List[Dict]:
        """
        Search for similar documents using semantic similarity.
        
        Args:
            query: Search query text
            n_results: Number of results to return
            
        Returns:
            List of similar documents with metadata and scores
        """
        if not self.collection:
            self.get_or_create_collection()
        
        logger.info(f"Searching for similar documents: '{query[:50]}...'")
        
        try:
            # Generate embedding for query
            query_embedding = self.embeddings.embed_query(query)
            
            # Search in collection
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results,
                include=['documents', 'metadatas', 'distances']
            )
            
            # Format results
            formatted_results = []
            for i in range(len(results['documents'][0])):
                result = {
                    'text': results['documents'][0][i],
                    'metadata': results['metadatas'][0][i],
                    'distance': results['distances'][0][i],
                    'similarity_score': 1 - results['distances'][0][i]  # Convert distance to similarity
                }
                formatted_results.append(result)
            
            logger.info(f"Found {len(formatted_results)} similar documents")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Failed to search documents: {e}")
            return []
    
    def get_collection_stats(self) -> Dict:
        """
        Get statistics about the vector collection.
        
        Returns:
            Dictionary with collection statistics
        """
        if not self.collection:
            self.get_or_create_collection()
        
        try:
            count = self.collection.count()
            
            # Get sample of documents to analyze
            sample_size = min(10, count)
            if count > 0:
                sample = self.collection.peek(limit=sample_size)
                
                # Calculate average text length
                avg_text_length = sum(len(doc) for doc in sample['documents']) / len(sample['documents'])
                
                # Get unique source files
                source_files = set()
                for metadata in sample['metadatas']:
                    if 'source_file' in metadata:
                        source_files.add(metadata['source_file'])
            else:
                avg_text_length = 0
                source_files = set()
            
            stats = {
                'total_documents': count,
                'collection_name': Config.COLLECTION_NAME,
                'average_text_length': avg_text_length,
                'unique_source_files': len(source_files),
                'source_files': list(source_files),
                'embedding_model': Config.EMBEDDING_MODEL,
                'chunk_size': Config.CHUNK_SIZE,
                'chunk_overlap': Config.CHUNK_OVERLAP
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {}
    
    def reset_collection(self) -> bool:
        """
        Reset (delete and recreate) the collection.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.warning(f"Resetting collection '{Config.COLLECTION_NAME}'...")
            
            # Delete existing collection
            try:
                self.client.delete_collection(name=Config.COLLECTION_NAME)
                logger.info("Existing collection deleted")
            except Exception:
                logger.info("No existing collection to delete")
            
            # Create new collection
            self.collection = self.client.create_collection(
                name=Config.COLLECTION_NAME,
                metadata={"description": "ManagerPort community documents for RAG chatbot"}
            )
            
            logger.info("✅ Collection reset successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to reset collection: {e}")
            return False

    def reset_and_reload_documents(self, documents: Dict[str, Dict]) -> bool:
        """
        Reset the collection and reload all documents.

        Args:
            documents: Dictionary of processed documents

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("Starting complete database reset and reload...")

            # Reset the collection
            if not self.reset_collection():
                logger.error("Failed to reset collection")
                return False

            # Process and add documents
            if documents:
                logger.info(f"Reloading {len(documents)} documents...")

                # Chunk documents
                chunks = self.chunk_documents(documents)
                logger.info(f"Created {len(chunks)} chunks from documents")

                # Add chunks to collection
                success = self.add_documents_to_collection(chunks)

                if success:
                    logger.info("✅ Database reset and reload completed successfully")

                    # Log summary
                    collection_stats = self.get_collection_stats()
                    logger.info(f"📊 Collection now contains {collection_stats.get('total_documents', 0)} documents")

                    return True
                else:
                    logger.error("Failed to add documents to collection")
                    return False
            else:
                logger.warning("No documents provided for reload")
                return True

        except Exception as e:
            logger.error(f"Failed to reset and reload documents: {e}")
            return False

    def force_rebuild_database(self) -> bool:
        """
        Force rebuild the entire database from scratch.
        This will process all available documents and rebuild the vector database.

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.warning("🔄 Starting force rebuild of vector database...")

            # Import here to avoid circular imports
            from .document_processor import DocumentProcessor

            # Process all documents
            processor = DocumentProcessor()
            documents = processor.process_all_documents()

            if not documents:
                logger.error("No documents found to process")
                return False

            # Reset and reload
            success = self.reset_and_reload_documents(documents)

            if success:
                logger.info("✅ Force rebuild completed successfully")

                # Save processed texts for inspection
                processor.save_processed_texts()

                return True
            else:
                logger.error("Force rebuild failed")
                return False

        except Exception as e:
            logger.error(f"Failed to force rebuild database: {e}")
            return False
