"""Gradio web interface for the ManagerPort RAG chatbot."""

import gradio as gr
import time
from typing import List, Tuple, Dict, Optional
from datetime import datetime

from .config import Config
from .rag_pipeline import RAGPipeline
from .model_selector import ModelSelector
from .utils.logger import logger


class ChatInterface:
    """Gradio chat interface for the ManagerPort RAG chatbot."""
    
    def __init__(self):
        """Initialize the chat interface."""
        self.rag_pipeline = None
        self.model_selector = ModelSelector()
        self.chat_history = []
        self._initialize_rag()
    
    def _initialize_rag(self):
        """Initialize the RAG pipeline."""
        try:
            logger.info("Initializing RAG pipeline for Gradio interface...")
            self.rag_pipeline = RAGPipeline()
            logger.info("✅ RAG pipeline ready for chat interface")
        except Exception as e:
            logger.error(f"Failed to initialize RAG pipeline: {e}")
            self.rag_pipeline = None

    def change_model(self, model_id: str) -> str:
        """
        Change the OpenAI model used by the RAG pipeline.

        Args:
            model_id: The new model ID to use

        Returns:
            Status message about the model change
        """
        try:
            if not self.model_selector.select_model(model_id):
                return f"❌ Invalid model: {model_id}"

            # Reinitialize RAG pipeline with new model
            old_model = Config.OPENAI_MODEL
            Config.OPENAI_MODEL = model_id

            self._initialize_rag()

            if self.rag_pipeline:
                model_info = self.model_selector.get_model_info(model_id)
                model_name = model_info.get('name', model_id) if model_info else model_id
                return f"✅ Model changed to: {model_name} ({model_id})"
            else:
                # Revert on failure
                Config.OPENAI_MODEL = old_model
                self._initialize_rag()
                return f"❌ Failed to initialize with model: {model_id}"

        except Exception as e:
            logger.error(f"Error changing model: {e}")
            return f"❌ Error changing model: {str(e)}"
    
    def chat_response(self, message: str, history: List[dict]) -> Tuple[str, List[dict]]:
        """
        Process chat message and return response.

        Args:
            message: User's message
            history: Chat history in messages format

        Returns:
            Tuple of (empty string for clearing input, updated history)
        """
        if not self.rag_pipeline:
            error_msg = "❌ RAG pipeline nie je dostupný. Skontrolujte konfiguráciu."
            history.append({"role": "user", "content": message})
            history.append({"role": "assistant", "content": error_msg})
            return "", history

        if not message.strip():
            return "", history

        try:
            # Add user message to history
            history.append({"role": "user", "content": message})
            history.append({"role": "assistant", "content": "🤔 Premýšľam..."})

            # Get response from RAG pipeline
            logger.info(f"Processing chat message: {message[:50]}...")
            response = self.rag_pipeline.ask_question(message)

            # Format response with sources
            formatted_response = self._format_response(response)

            # Update history with actual response
            history[-1]["content"] = formatted_response

            logger.info("✅ Chat response generated successfully")

        except Exception as e:
            logger.error(f"Chat response failed: {e}")
            error_msg = f"❌ Ospravedlňujem sa, nastala chyba: {str(e)}"
            history[-1]["content"] = error_msg

        return "", history
    
    def _format_response(self, response: dict) -> str:
        """Format the RAG response for display."""
        answer = response.get('answer', 'Nenašiel som odpoveď.')
        sources = response.get('sources', [])
        
        formatted = f"{answer}\n\n"
        
        if sources:
            formatted += "📚 **Zdroje:**\n"
            for i, source in enumerate(sources[:3], 1):  # Show max 3 sources
                file_name = source['file'].replace('.pdf', '').replace('-', ' ')
                year = source['year']
                similarity = source['similarity']
                formatted += f"{i}. {file_name} ({year}) - Podobnosť: {similarity:.1%}\n"
        
        return formatted
    
    def clear_chat(self) -> List[dict]:
        """Clear chat history."""
        logger.info("Chat history cleared")
        return []
    
    def get_example_questions(self) -> List[str]:
        """Get example questions for the interface."""
        return [
            "Aké sú najčastejšie problémy v projektovom manažmente?",
            "Ako riešiť konflikty v tíme?",
            "Čo je dôležité pri vedení ľudí?",
            "Aké sú trendy v manažmente?",
            "Ako motivovať zamestnancov?",
            "Čo je agile metodika?",
            "Ako robiť efektívne porady?",
            "Aké sú kľúčové zručnosti manažéra?"
        ]
    
    def create_interface(self) -> gr.Blocks:
        """Create the Gradio interface."""
        
        # Custom CSS for better styling
        css = """
        .gradio-container {
            max-width: 1200px !important;
            margin: auto !important;
        }
        .chat-container {
            height: 600px !important;
        }
        .example-btn {
            margin: 2px !important;
            font-size: 12px !important;
        }
        """
        
        with gr.Blocks(
            title="ManagerPort RAG Chatbot",
            theme=gr.themes.Soft(),
            css=css
        ) as interface:
            
            # Header
            gr.Markdown(
                """
                # 🎯 ManagerPort RAG Chatbot
                
                Inteligentný asistent pre ManagerPort komunitu založený na dokumentoch z rokov 2017-2024.
                Pýtajte sa na témy týkajúce sa manažmentu, projektového riadenia, vedenia tímov a ďalších oblastí.
                """
            )
            
            # Main chat interface
            with gr.Row():
                with gr.Column(scale=4):
                    chatbot = gr.Chatbot(
                        label="Konverzácia",
                        height=500,
                        show_copy_button=True,
                        elem_classes=["chat-container"],
                        type="messages"
                    )
                    
                    with gr.Row():
                        msg = gr.Textbox(
                            label="Vaša otázka",
                            placeholder="Napíšte svoju otázku o manažmente...",
                            scale=4,
                            lines=2
                        )
                        send_btn = gr.Button("Odoslať", variant="primary", scale=1)
                    
                    with gr.Row():
                        clear_btn = gr.Button("Vymazať históriu", variant="secondary")
                
                # Sidebar with examples and info
                with gr.Column(scale=1):
                    gr.Markdown("### 💡 Príklady otázok")
                    
                    example_questions = self.get_example_questions()
                    for question in example_questions:
                        example_btn = gr.Button(
                            question,
                            elem_classes=["example-btn"],
                            size="sm"
                        )
                        example_btn.click(
                            lambda q=question: q,
                            outputs=msg
                        )
                    
                    # Model selection
                    gr.Markdown("### 🤖 Výber modelu")

                    # Get available models for dropdown
                    available_models = self.model_selector.list_available_models()
                    model_choices = []
                    model_labels = {}

                    for model_id, model_info in available_models.items():
                        label = f"{model_info['name']}"
                        if model_info.get('recommended'):
                            label += " ⭐"
                        model_choices.append(model_id)
                        model_labels[model_id] = label

                    model_dropdown = gr.Dropdown(
                        choices=[(model_labels[mid], mid) for mid in model_choices],
                        value=Config.OPENAI_MODEL,
                        label="OpenAI Model",
                        interactive=True
                    )

                    model_status = gr.Markdown(f"**Aktuálny model:** {Config.OPENAI_MODEL}")

                    # Model change handler
                    def handle_model_change(new_model):
                        status_msg = self.change_model(new_model)
                        return status_msg

                    model_dropdown.change(
                        handle_model_change,
                        inputs=model_dropdown,
                        outputs=model_status
                    )

                    # System info
                    gr.Markdown("### ℹ️ Informácie")
                    if self.rag_pipeline:
                        info = self.rag_pipeline.get_pipeline_info()
                        gr.Markdown(f"""
                        **Dokumenty:** {info.get('vector_database', {}).get('total_documents', 0)}
                        **Súbory:** {info.get('vector_database', {}).get('unique_files', 0)}
                        **Status:** ✅ Pripravený
                        """)
                    else:
                        gr.Markdown("**Status:** ❌ Nedostupný")
            
            # Event handlers
            msg.submit(
                self.chat_response,
                inputs=[msg, chatbot],
                outputs=[msg, chatbot]
            )
            
            send_btn.click(
                self.chat_response,
                inputs=[msg, chatbot],
                outputs=[msg, chatbot]
            )
            
            clear_btn.click(
                self.clear_chat,
                outputs=chatbot
            )
            
            # Footer
            gr.Markdown(
                """
                ---
                **ManagerPort RAG Chatbot** | Postavený na OpenAI GPT-4 a Chroma vector database  
                Dokumenty: ManagerPort komunita 2017-2024 | Vyvinul [Grapph - Milos Wikarski](https://grapph.com) s ❤️ pre manažérsku komunitu
                """
            )
        
        return interface
    
    def launch(self, **kwargs):
        """Launch the Gradio interface."""
        interface = self.create_interface()
        
        # Default launch parameters
        launch_params = {
            "server_name": Config.GRADIO_SERVER_NAME,
            "server_port": Config.GRADIO_SERVER_PORT,
            "share": Config.GRADIO_SHARE,
            "show_error": True,
            "quiet": False,
        }
        
        # Override with any provided parameters
        launch_params.update(kwargs)
        
        logger.info(f"Launching Gradio interface on {launch_params['server_name']}:{launch_params['server_port']}")
        
        try:
            interface.launch(**launch_params)
        except Exception as e:
            logger.error(f"Failed to launch Gradio interface: {e}")
            raise


def create_chat_interface() -> ChatInterface:
    """Create and return a chat interface instance."""
    return ChatInterface()


def launch_chat_app(**kwargs):
    """Launch the chat application."""
    chat_interface = create_chat_interface()
    chat_interface.launch(**kwargs)
