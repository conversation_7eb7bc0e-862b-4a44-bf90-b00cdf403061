"""Markdown document processor for the RAG chatbot."""

import re
from pathlib import Path
from typing import Dict, <PERSON><PERSON>, Optional
from datetime import datetime

from .config import Config
from .utils.logger import logger


class MarkdownProcessor:
    """Process markdown documents for RAG chatbot."""
    
    def __init__(self):
        """Initialize the markdown processor."""
        self.processed_docs = {}
        logger.info("MarkdownProcessor initialized")
    
    def extract_text_from_markdown(self, md_path: Path) -> Tuple[str, Dict]:
        """
        Extract text and metadata from a markdown file.
        
        Args:
            md_path: Path to the markdown file
            
        Returns:
            Tuple of (extracted_text, metadata)
        """
        try:
            logger.info(f"Processing Markdown: {md_path.name}")
            
            # Read the markdown file
            with open(md_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # Extract metadata
            metadata = {
                "filename": md_path.name,
                "file_size": md_path.stat().st_size,
                "creation_date": datetime.fromtimestamp(md_path.stat().st_ctime).isoformat(),
                "modification_date": datetime.fromtimestamp(md_path.stat().st_mtime).isoformat(),
                "file_type": "markdown",
                "encoding": "utf-8"
            }
            
            # Count lines and estimate sections
            lines = content.split('\n')
            non_empty_lines = [line for line in lines if line.strip()]
            
            # Count markdown headers to estimate sections
            headers = [line for line in lines if line.strip().startswith('#')]
            
            # Add processing statistics to metadata
            metadata.update({
                "total_characters": len(content),
                "total_lines": len(lines),
                "non_empty_lines": len(non_empty_lines),
                "estimated_sections": len(headers),
                "has_headers": len(headers) > 0
            })
            
            logger.info(f"Extracted {len(content)} characters from {len(non_empty_lines)} non-empty lines")
            
            return content, metadata
            
        except Exception as e:
            logger.error(f"Failed to extract text from {md_path.name}: {e}")
            raise
    
    def clean_text(self, text: str) -> str:
        """
        Clean and normalize markdown text for better RAG performance.
        
        Args:
            text: Raw markdown text
            
        Returns:
            Cleaned text
        """
        if not text:
            return ""
        
        # Remove markdown syntax while preserving structure
        # Remove markdown headers but keep the text
        text = re.sub(r'^#{1,6}\s+', '', text, flags=re.MULTILINE)
        
        # Remove markdown links but keep the text
        text = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', text)
        
        # Remove markdown emphasis but keep the text
        text = re.sub(r'\*\*([^\*]+)\*\*', r'\1', text)  # Bold
        text = re.sub(r'\*([^\*]+)\*', r'\1', text)      # Italic
        text = re.sub(r'__([^_]+)__', r'\1', text)       # Bold
        text = re.sub(r'_([^_]+)_', r'\1', text)         # Italic
        
        # Remove code blocks but keep the content
        text = re.sub(r'```[^\n]*\n(.*?)\n```', r'\1', text, flags=re.DOTALL)
        text = re.sub(r'`([^`]+)`', r'\1', text)  # Inline code
        
        # Remove markdown lists markers but keep the text
        text = re.sub(r'^\s*[-*+]\s+', '', text, flags=re.MULTILINE)
        text = re.sub(r'^\s*\d+\.\s+', '', text, flags=re.MULTILINE)
        
        # Remove blockquotes markers but keep the text
        text = re.sub(r'^\s*>\s*', '', text, flags=re.MULTILINE)
        
        # Remove horizontal rules
        text = re.sub(r'^[-*_]{3,}$', '', text, flags=re.MULTILINE)
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove common markdown artifacts
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', text)  # Control characters
        
        # Normalize Slovak/Czech characters (common in ManagerPort content)
        text = text.replace('ľ', 'ľ').replace('š', 'š').replace('č', 'č')
        text = text.replace('ť', 'ť').replace('ž', 'ž').replace('ý', 'ý')
        text = text.replace('á', 'á').replace('í', 'í').replace('é', 'é')
        text = text.replace('ó', 'ó').replace('ú', 'ú').replace('ň', 'ň')
        text = text.replace('ď', 'ď').replace('ô', 'ô').replace('ŕ', 'ŕ')
        
        # Remove excessive line breaks
        text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)
        
        # Clean up common formatting issues
        text = re.sub(r'([a-z])([A-Z])', r'\1 \2', text)  # Add space between camelCase
        text = re.sub(r'(\d+)([A-Za-z])', r'\1 \2', text)  # Add space between numbers and letters
        
        # Remove URLs and email addresses for cleaner text
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '', text)
        
        # Final cleanup
        text = text.strip()
        text = re.sub(r'\s+', ' ', text)  # Final whitespace normalization
        
        return text
    
    def save_processed_text(self) -> Path:
        """
        Save processed markdown texts to files for inspection.
        
        Returns:
            Path to the output directory
        """
        output_dir = Config.DATA_DIR / "processed_texts" / "markdown"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Saving processed markdown texts to {output_dir}")
        
        for filename, doc_data in self.processed_docs.items():
            # Save cleaned text
            output_file = output_dir / f"{filename}.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(doc_data['cleaned_text'])
            
            # Save metadata
            metadata_file = output_dir / f"{filename}.metadata.txt"
            with open(metadata_file, 'w', encoding='utf-8') as f:
                for key, value in doc_data['metadata'].items():
                    f.write(f"{key}: {value}\n")
        
        logger.info(f"Saved {len(self.processed_docs)} processed markdown files")
        return output_dir
    
    def process_all_markdowns(self) -> Dict[str, Dict]:
        """
        Process all markdown files in the markdown directory.
        
        Returns:
            Dictionary of processed documents with metadata
        """
        md_files = Config.get_md_files()
        
        if not md_files:
            logger.warning("No markdown files found in assets/md directory")
            return {}
        
        logger.info(f"Found {len(md_files)} markdown files to process")
        
        processed_docs = {}
        total_chars = 0
        total_lines = 0
        
        for md_path in md_files:
            try:
                # Extract raw text and metadata
                raw_text, metadata = self.extract_text_from_markdown(md_path)
                
                # Clean the text
                cleaned_text = self.clean_text(raw_text)
                
                # Store processed document
                doc_data = {
                    "filename": md_path.name,
                    "raw_text": raw_text,
                    "cleaned_text": cleaned_text,
                    "metadata": metadata,
                    "processing_stats": {
                        "raw_char_count": len(raw_text),
                        "cleaned_char_count": len(cleaned_text),
                        "compression_ratio": len(cleaned_text) / len(raw_text) if raw_text else 0
                    }
                }
                
                processed_docs[md_path.name] = doc_data
                total_chars += len(cleaned_text)
                total_lines += metadata.get("non_empty_lines", 0)
                
                logger.info(f"✓ Processed {md_path.name}: {len(cleaned_text)} chars, {metadata.get('non_empty_lines', 0)} lines")
                
            except Exception as e:
                logger.error(f"✗ Failed to process {md_path.name}: {e}")
                continue
        
        # Store processing summary
        self.processed_docs = processed_docs
        
        logger.info(f"📊 Processing complete: {len(processed_docs)} files, {total_chars:,} total characters, {total_lines} lines")
        
        return processed_docs
    
    def get_document_by_filename(self, filename: str) -> Optional[Dict]:
        """
        Get processed document data by filename.
        
        Args:
            filename: Name of the markdown file
            
        Returns:
            Document data or None if not found
        """
        return self.processed_docs.get(filename)
    
    def get_all_documents(self) -> Dict[str, Dict]:
        """
        Get all processed documents.
        
        Returns:
            Dictionary of all processed documents
        """
        return self.processed_docs
