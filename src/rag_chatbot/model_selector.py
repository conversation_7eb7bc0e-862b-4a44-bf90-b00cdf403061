"""Model selection utility for the RAG chatbot."""

import os
from typing import Dict, List, Optional, Tuple
from .config import Config
from .utils.logger import logger


class ModelSelector:
    """Utility class for selecting and managing OpenAI models."""
    
    def __init__(self):
        """Initialize the model selector."""
        self.current_model = Config.OPENAI_MODEL
    
    def list_available_models(self) -> Dict[str, Dict]:
        """List all available models with their information."""
        return Config.get_available_models()
    
    def list_recommended_models(self) -> Dict[str, Dict]:
        """List recommended models for RAG applications."""
        return Config.get_recommended_models()
    
    def get_model_info(self, model_id: str) -> Optional[Dict]:
        """Get detailed information about a specific model."""
        return Config.get_model_info(model_id)
    
    def select_model(self, model_id: str) -> bool:
        """
        Select a model for use.
        
        Args:
            model_id: The model identifier
            
        Returns:
            True if model was successfully selected, False otherwise
        """
        if not Config.is_valid_model(model_id):
            logger.error(f"Invalid model ID: {model_id}")
            return False
        
        self.current_model = model_id
        logger.info(f"Selected model: {model_id}")
        return True
    
    def get_current_model(self) -> str:
        """Get the currently selected model."""
        return self.current_model
    
    def estimate_cost(self, model_id: str, input_tokens: int, output_tokens: int) -> float:
        """Estimate the cost for using a model with given token counts."""
        return Config.get_model_cost_estimate(model_id, input_tokens, output_tokens)
    
    def compare_models(self, model_ids: List[str]) -> Dict[str, Dict]:
        """
        Compare multiple models side by side.
        
        Args:
            model_ids: List of model IDs to compare
            
        Returns:
            Dictionary with model comparison data
        """
        comparison = {}
        
        for model_id in model_ids:
            model_info = self.get_model_info(model_id)
            if model_info:
                comparison[model_id] = {
                    "name": model_info.get("name", model_id),
                    "description": model_info.get("description", ""),
                    "context_window": model_info.get("context_window", 0),
                    "input_cost_per_1k": model_info.get("cost_per_1k_tokens", {}).get("input", 0),
                    "output_cost_per_1k": model_info.get("cost_per_1k_tokens", {}).get("output", 0),
                    "recommended": model_info.get("recommended", False)
                }
        
        return comparison
    
    def get_best_model_for_budget(self, max_cost_per_1k_tokens: float) -> Optional[str]:
        """
        Find the best model within a given budget.
        
        Args:
            max_cost_per_1k_tokens: Maximum cost per 1k tokens (input + output average)
            
        Returns:
            Model ID of the best model within budget, or None if none found
        """
        available_models = self.list_available_models()
        suitable_models = []
        
        for model_id, model_info in available_models.items():
            costs = model_info.get("cost_per_1k_tokens", {})
            if costs:
                avg_cost = (costs.get("input", 0) + costs.get("output", 0)) / 2
                if avg_cost <= max_cost_per_1k_tokens:
                    suitable_models.append((model_id, model_info, avg_cost))
        
        if not suitable_models:
            return None
        
        # Sort by context window (descending) and then by cost (ascending)
        suitable_models.sort(key=lambda x: (-x[1].get("context_window", 0), x[2]))
        
        return suitable_models[0][0]
    
    def print_model_comparison(self, model_ids: Optional[List[str]] = None):
        """
        Print a formatted comparison of models.
        
        Args:
            model_ids: List of model IDs to compare. If None, compares all models.
        """
        if model_ids is None:
            model_ids = list(self.list_available_models().keys())
        
        comparison = self.compare_models(model_ids)
        
        print("🤖 OpenAI Model Comparison")
        print("=" * 80)
        print(f"{'Model':<25} {'Context':<10} {'Input $/1k':<12} {'Output $/1k':<13} {'Recommended':<12}")
        print("-" * 80)
        
        for model_id, info in comparison.items():
            recommended = "✅ Yes" if info["recommended"] else "❌ No"
            print(f"{info['name']:<25} {info['context_window']:<10} "
                  f"${info['input_cost_per_1k']:<11.5f} ${info['output_cost_per_1k']:<12.5f} {recommended:<12}")
        
        print("-" * 80)
        print(f"Current model: {self.current_model}")
    
    def print_model_details(self, model_id: str):
        """Print detailed information about a specific model."""
        model_info = self.get_model_info(model_id)
        
        if not model_info:
            print(f"❌ Model '{model_id}' not found")
            return
        
        print(f"🤖 {model_info.get('name', model_id)}")
        print("=" * 50)
        print(f"Description: {model_info.get('description', 'N/A')}")
        print(f"Context Window: {model_info.get('context_window', 'N/A'):,} tokens")
        
        costs = model_info.get('cost_per_1k_tokens', {})
        if costs:
            print(f"Input Cost: ${costs.get('input', 0):.5f} per 1k tokens")
            print(f"Output Cost: ${costs.get('output', 0):.5f} per 1k tokens")
        
        print(f"Recommended: {'✅ Yes' if model_info.get('recommended', False) else '❌ No'}")
        
        # Example cost calculation
        if costs:
            example_cost = self.estimate_cost(model_id, 1000, 500)
            print(f"\nExample cost (1k input + 500 output tokens): ${example_cost:.4f}")


def interactive_model_selection() -> str:
    """
    Interactive model selection interface.
    
    Returns:
        Selected model ID
    """
    selector = ModelSelector()
    
    print("🤖 OpenAI Model Selection")
    print("=" * 50)
    
    # Show recommended models first
    recommended = selector.list_recommended_models()
    if recommended:
        print("\n✅ Recommended Models:")
        for i, (model_id, model_info) in enumerate(recommended.items(), 1):
            costs = model_info.get('cost_per_1k_tokens', {})
            cost_info = f"(${costs.get('input', 0):.4f}/${costs.get('output', 0):.4f} per 1k tokens)" if costs else ""
            print(f"  {i}. {model_info['name']} - {model_info['description']} {cost_info}")
    
    # Show all models
    print(f"\n📋 All Available Models:")
    all_models = list(selector.list_available_models().keys())
    for i, model_id in enumerate(all_models, 1):
        model_info = selector.get_model_info(model_id)
        print(f"  {i}. {model_info['name']} ({model_id})")
    
    print(f"\nCurrent model: {selector.get_current_model()}")
    
    while True:
        try:
            choice = input(f"\nSelect model (1-{len(all_models)}) or 'q' to quit: ").strip()
            
            if choice.lower() == 'q':
                return selector.get_current_model()
            
            choice_num = int(choice)
            if 1 <= choice_num <= len(all_models):
                selected_model = all_models[choice_num - 1]
                selector.print_model_details(selected_model)
                
                confirm = input(f"\nUse {selected_model}? (y/n): ").strip().lower()
                if confirm == 'y':
                    selector.select_model(selected_model)
                    return selected_model
            else:
                print("Invalid choice. Please try again.")
                
        except (ValueError, KeyboardInterrupt):
            print("\nExiting model selection.")
            return selector.get_current_model()


if __name__ == "__main__":
    # Demo the model selector
    selector = ModelSelector()
    selector.print_model_comparison()
    
    print("\n" + "="*50)
    selected = interactive_model_selection()
    print(f"\nFinal selection: {selected}")
