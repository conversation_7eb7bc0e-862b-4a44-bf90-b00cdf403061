"""Unified document processor for both PDF and Markdown files."""

from typing import Dict, Optional
from pathlib import Path

from .config import Config
from .pdf_processor import PDFProcessor
from .markdown_processor import MarkdownProcessor
from .utils.logger import logger


class DocumentProcessor:
    """Unified processor for all document types (PDF and Markdown)."""
    
    def __init__(self):
        """Initialize the document processor."""
        self.pdf_processor = PDFProcessor()
        self.markdown_processor = MarkdownProcessor()
        self.all_processed_docs = {}
        logger.info("DocumentProcessor initialized")
    
    def process_all_documents(self) -> Dict[str, Dict]:
        """
        Process all documents (PDF and Markdown) in the assets directories.
        
        Returns:
            Dictionary of all processed documents with metadata
        """
        logger.info("Starting unified document processing...")
        
        all_docs = {}
        
        # Process PDF documents
        logger.info("Processing PDF documents...")
        pdf_docs = self.pdf_processor.process_all_pdfs()
        if pdf_docs:
            # Add document type to metadata
            for filename, doc_data in pdf_docs.items():
                doc_data['metadata']['document_type'] = 'pdf'
                doc_data['metadata']['source_processor'] = 'PDFProcessor'
            all_docs.update(pdf_docs)
            logger.info(f"✓ Processed {len(pdf_docs)} PDF documents")
        else:
            logger.warning("No PDF documents processed")
        
        # Process Markdown documents
        logger.info("Processing Markdown documents...")
        md_docs = self.markdown_processor.process_all_markdowns()
        if md_docs:
            # Add document type to metadata
            for filename, doc_data in md_docs.items():
                doc_data['metadata']['document_type'] = 'markdown'
                doc_data['metadata']['source_processor'] = 'MarkdownProcessor'
            all_docs.update(md_docs)
            logger.info(f"✓ Processed {len(md_docs)} Markdown documents")
        else:
            logger.warning("No Markdown documents processed")
        
        # Store combined results
        self.all_processed_docs = all_docs
        
        # Calculate summary statistics
        total_chars = sum(doc['processing_stats']['cleaned_char_count'] for doc in all_docs.values())
        pdf_count = len(pdf_docs) if pdf_docs else 0
        md_count = len(md_docs) if md_docs else 0
        
        logger.info(f"📊 Document processing complete:")
        logger.info(f"   - PDF files: {pdf_count}")
        logger.info(f"   - Markdown files: {md_count}")
        logger.info(f"   - Total files: {len(all_docs)}")
        logger.info(f"   - Total characters: {total_chars:,}")
        
        return all_docs
    
    def save_processed_texts(self) -> Dict[str, Path]:
        """
        Save all processed texts to files for inspection.
        
        Returns:
            Dictionary with paths to output directories for each document type
        """
        output_paths = {}
        
        # Save PDF processed texts
        if self.pdf_processor.processed_docs:
            pdf_output = self.pdf_processor.save_processed_text()
            output_paths['pdf'] = pdf_output
            logger.info(f"PDF texts saved to: {pdf_output}")
        
        # Save Markdown processed texts
        if self.markdown_processor.processed_docs:
            md_output = self.markdown_processor.save_processed_text()
            output_paths['markdown'] = md_output
            logger.info(f"Markdown texts saved to: {md_output}")
        
        return output_paths
    
    def get_document_by_filename(self, filename: str) -> Optional[Dict]:
        """
        Get processed document data by filename.
        
        Args:
            filename: Name of the document file
            
        Returns:
            Document data or None if not found
        """
        return self.all_processed_docs.get(filename)
    
    def get_all_documents(self) -> Dict[str, Dict]:
        """
        Get all processed documents.
        
        Returns:
            Dictionary of all processed documents
        """
        return self.all_processed_docs
    
    def get_documents_by_type(self, doc_type: str) -> Dict[str, Dict]:
        """
        Get documents filtered by type.
        
        Args:
            doc_type: Document type ('pdf' or 'markdown')
            
        Returns:
            Dictionary of documents of the specified type
        """
        return {
            filename: doc_data 
            for filename, doc_data in self.all_processed_docs.items()
            if doc_data.get('metadata', {}).get('document_type') == doc_type
        }
    
    def get_processing_summary(self) -> Dict:
        """
        Get summary of document processing results.
        
        Returns:
            Dictionary with processing statistics
        """
        if not self.all_processed_docs:
            return {
                "total_documents": 0,
                "pdf_documents": 0,
                "markdown_documents": 0,
                "total_characters": 0,
                "average_chars_per_doc": 0
            }
        
        pdf_docs = self.get_documents_by_type('pdf')
        md_docs = self.get_documents_by_type('markdown')
        
        total_chars = sum(
            doc['processing_stats']['cleaned_char_count'] 
            for doc in self.all_processed_docs.values()
        )
        
        return {
            "total_documents": len(self.all_processed_docs),
            "pdf_documents": len(pdf_docs),
            "markdown_documents": len(md_docs),
            "total_characters": total_chars,
            "average_chars_per_doc": total_chars // len(self.all_processed_docs) if self.all_processed_docs else 0,
            "document_types": {
                "pdf": {
                    "count": len(pdf_docs),
                    "characters": sum(doc['processing_stats']['cleaned_char_count'] for doc in pdf_docs.values())
                },
                "markdown": {
                    "count": len(md_docs),
                    "characters": sum(doc['processing_stats']['cleaned_char_count'] for doc in md_docs.values())
                }
            }
        }
