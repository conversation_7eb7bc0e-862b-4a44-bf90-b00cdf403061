"""Configuration management for the RAG chatbot."""

import os
from pathlib import Path
from typing import Optional, List, Dict

from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class Config:
    """Configuration class for the RAG chatbot."""
    
    # Project paths
    PROJECT_ROOT = Path(__file__).parent.parent.parent
    ASSETS_DIR = PROJECT_ROOT / "assets"
    PDF_DIR = ASSETS_DIR / "pdf"
    MD_DIR = ASSETS_DIR / "md"
    DATA_DIR = PROJECT_ROOT / "data"
    LOGS_DIR = PROJECT_ROOT / "logs"
    
    # OpenAI Configuration
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    OPENAI_MODEL: str = os.getenv("OPENAI_MODEL", "gpt-4.1-nano")
    EMBEDDING_MODEL: str = os.getenv("EMBEDDING_MODEL", "text-embedding-ada-002")
    LLM_MODEL: str = os.getenv("LLM_MODEL", "gpt-4-turbo-preview")  # Legacy support
    TEMPERATURE: float = float(os.getenv("TEMPERATURE", "0.1"))

    # Available OpenAI Models (as of 2024)
    AVAILABLE_MODELS = {
        # GPT-4o Series (Latest, most capable)
        "gpt-4o-mini": {
            "name": "GPT-4o Mini",
            "description": "Fast and cost-effective, 128k context",
            "context_window": 128000,
            "cost_per_1k_tokens": {"input": 0.00015, "output": 0.0006},
            "recommended": True
        },
        "gpt-4.1-nano": {
            "name": "GPT-4.1 Nano",
            "description": "Fast and cost-effective",
            "context_window": 128000,
            "cost_per_1k_tokens": {"input": 0.00010, "output": 0.00025},
            "recommended": True
        },
        "gpt-4o": {
            "name": "GPT-4o",
            "description": "Most capable model, multimodal, 128k context",
            "context_window": 128000,
            "cost_per_1k_tokens": {"input": 0.005, "output": 0.015},
            "recommended": True
        },

        "chatgpt-4o-latest": {
            "name": "ChatGPT-4o Latest",
            "description": "Latest ChatGPT-4o model with dynamic updates",
            "context_window": 128000,
            "cost_per_1k_tokens": {"input": 0.005, "output": 0.015},
            "recommended": False
        },

        # GPT-4 Turbo Series
        "gpt-4-turbo": {
            "name": "GPT-4 Turbo",
            "description": "High-intelligence model, 128k context",
            "context_window": 128000,
            "cost_per_1k_tokens": {"input": 0.01, "output": 0.03},
            "recommended": False
        },
        "gpt-4-turbo-2024-04-09": {
            "name": "GPT-4 Turbo (April 2024)",
            "description": "Specific GPT-4 Turbo version",
            "context_window": 128000,
            "cost_per_1k_tokens": {"input": 0.01, "output": 0.03},
            "recommended": False
        },

        # GPT-4 Classic
        "gpt-4": {
            "name": "GPT-4",
            "description": "Original GPT-4 model, 8k context",
            "context_window": 8192,
            "cost_per_1k_tokens": {"input": 0.03, "output": 0.06},
            "recommended": False
        },

        # GPT-3.5 Turbo
        "gpt-3.5-turbo": {
            "name": "GPT-3.5 Turbo",
            "description": "Fast and cost-effective, 16k context",
            "context_window": 16385,
            "cost_per_1k_tokens": {"input": 0.0005, "output": 0.0015},
            "recommended": False
        }
    }
    
    # Vector Database Configuration
    VECTOR_DB_PATH: Path = Path(os.getenv("VECTOR_DB_PATH", str(DATA_DIR / "chroma_db")))
    COLLECTION_NAME: str = os.getenv("COLLECTION_NAME", "managerport_docs")
    
    # Text Processing Configuration
    CHUNK_SIZE: int = int(os.getenv("CHUNK_SIZE", "1000"))
    CHUNK_OVERLAP: int = int(os.getenv("CHUNK_OVERLAP", "200"))

    # RAG Configuration
    RETRIEVAL_K: int = int(os.getenv("RETRIEVAL_K", "5"))
    
    # Gradio Configuration
    GRADIO_SERVER_NAME: str = os.getenv("GRADIO_SERVER_NAME", "0.0.0.0")
    GRADIO_SERVER_PORT: int = int(os.getenv("GRADIO_SERVER_PORT", "7860"))
    GRADIO_SHARE: bool = os.getenv("GRADIO_SHARE", "false").lower() == "true"
    
    # Logging Configuration
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    
    @classmethod
    def validate(cls) -> bool:
        """Validate required configuration."""
        if not cls.OPENAI_API_KEY:
            raise ValueError("OPENAI_API_KEY is required")
        
        # Create necessary directories
        cls.DATA_DIR.mkdir(exist_ok=True)
        cls.LOGS_DIR.mkdir(exist_ok=True)
        cls.VECTOR_DB_PATH.parent.mkdir(parents=True, exist_ok=True)
        
        return True
    
    @classmethod
    def get_pdf_files(cls) -> list[Path]:
        """Get list of PDF files in the assets directory."""
        if not cls.PDF_DIR.exists():
            return []
        return list(cls.PDF_DIR.glob("*.pdf"))

    @classmethod
    def get_md_files(cls) -> list[Path]:
        """Get list of Markdown files in the assets directory."""
        if not cls.MD_DIR.exists():
            return []
        return list(cls.MD_DIR.glob("*.md"))

    @classmethod
    def get_available_models(cls) -> Dict[str, Dict]:
        """Get dictionary of available OpenAI models."""
        return cls.AVAILABLE_MODELS.copy()

    @classmethod
    def get_recommended_models(cls) -> Dict[str, Dict]:
        """Get dictionary of recommended OpenAI models."""
        return {
            model_id: model_info
            for model_id, model_info in cls.AVAILABLE_MODELS.items()
            if model_info.get("recommended", False)
        }

    @classmethod
    def get_model_info(cls, model_id: str) -> Dict:
        """Get information about a specific model."""
        return cls.AVAILABLE_MODELS.get(model_id, {})

    @classmethod
    def is_valid_model(cls, model_id: str) -> bool:
        """Check if a model ID is valid."""
        return model_id in cls.AVAILABLE_MODELS

    @classmethod
    def get_model_cost_estimate(cls, model_id: str, input_tokens: int, output_tokens: int) -> float:
        """Estimate cost for using a model with given token counts."""
        model_info = cls.get_model_info(model_id)
        if not model_info or "cost_per_1k_tokens" not in model_info:
            return 0.0

        costs = model_info["cost_per_1k_tokens"]
        input_cost = (input_tokens / 1000) * costs["input"]
        output_cost = (output_tokens / 1000) * costs["output"]
        return input_cost + output_cost
