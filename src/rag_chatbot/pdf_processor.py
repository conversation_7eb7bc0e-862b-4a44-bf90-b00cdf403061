"""PDF text extraction and processing module using PyMuPDF."""

import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import fitz  # PyMuPDF
from loguru import logger

from .config import Config


class PDFProcessor:
    """Handles PDF text extraction and preprocessing for RAG system."""
    
    def __init__(self):
        """Initialize the PDF processor."""
        self.processed_docs: Dict[str, Dict] = {}
        
    def extract_text_from_pdf(self, pdf_path: Path) -> Tuple[str, Dict]:
        """
        Extract text from a single PDF file.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Tuple of (extracted_text, metadata)
        """
        try:
            logger.info(f"Processing PDF: {pdf_path.name}")
            
            # Open PDF document
            doc = fitz.open(pdf_path)
            
            # Extract metadata
            metadata = {
                "filename": pdf_path.name,
                "page_count": doc.page_count,
                "title": doc.metadata.get("title", ""),
                "author": doc.metadata.get("author", ""),
                "subject": doc.metadata.get("subject", ""),
                "creator": doc.metadata.get("creator", ""),
                "producer": doc.metadata.get("producer", ""),
                "creation_date": doc.metadata.get("creationDate", ""),
                "modification_date": doc.metadata.get("modDate", ""),
                "file_size": pdf_path.stat().st_size,
            }
            
            # Extract text from all pages
            full_text = ""
            page_texts = []
            
            for page_num in range(doc.page_count):
                page = doc[page_num]
                page_text = page.get_text()
                
                if page_text.strip():  # Only add non-empty pages
                    page_texts.append({
                        "page_number": page_num + 1,
                        "text": page_text,
                        "char_count": len(page_text)
                    })
                    full_text += f"\n--- Page {page_num + 1} ---\n{page_text}\n"
            
            doc.close()
            
            # Add processing statistics to metadata
            metadata.update({
                "total_characters": len(full_text),
                "pages_with_text": len(page_texts),
                "average_chars_per_page": len(full_text) / len(page_texts) if page_texts else 0
            })
            
            logger.info(f"Extracted {len(full_text)} characters from {len(page_texts)} pages")
            
            return full_text, metadata
            
        except Exception as e:
            logger.error(f"Error processing PDF {pdf_path.name}: {e}")
            raise
    
    def clean_text(self, text: str) -> str:
        """
        Clean and preprocess extracted text.
        
        Args:
            text: Raw extracted text
            
        Returns:
            Cleaned text
        """
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove page headers/footers patterns (common in ManagerPort docs)
        text = re.sub(r'--- Page \d+ ---', '', text)
        
        # Remove common PDF artifacts
        text = re.sub(r'\x0c', '', text)  # Form feed characters
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\xff]', '', text)  # Control characters
        
        # Normalize Slovak/Czech characters (common in ManagerPort content)
        text = text.replace('ľ', 'ľ').replace('š', 'š').replace('č', 'č')
        text = text.replace('ť', 'ť').replace('ž', 'ž').replace('ý', 'ý')
        text = text.replace('á', 'á').replace('í', 'í').replace('é', 'é')
        text = text.replace('ó', 'ó').replace('ú', 'ú').replace('ň', 'ň')
        text = text.replace('ď', 'ď').replace('ô', 'ô').replace('ŕ', 'ŕ')
        
        # Remove excessive line breaks
        text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)
        
        # Clean up common formatting issues
        text = re.sub(r'([a-z])([A-Z])', r'\1 \2', text)  # Add space between camelCase
        text = re.sub(r'(\d+)([A-Za-z])', r'\1 \2', text)  # Add space between numbers and letters
        
        # Remove URLs and email addresses for cleaner text
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        text = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '', text)
        
        # Final cleanup
        text = text.strip()
        text = re.sub(r'\s+', ' ', text)  # Final whitespace normalization
        
        return text
    
    def process_all_pdfs(self) -> Dict[str, Dict]:
        """
        Process all PDF files in the assets directory.
        
        Returns:
            Dictionary mapping filenames to processed document data
        """
        pdf_files = Config.get_pdf_files()
        
        if not pdf_files:
            logger.warning("No PDF files found in assets directory")
            return {}
        
        logger.info(f"Processing {len(pdf_files)} PDF files...")
        
        processed_docs = {}
        total_chars = 0
        total_pages = 0
        
        for pdf_path in pdf_files:
            try:
                # Extract raw text and metadata
                raw_text, metadata = self.extract_text_from_pdf(pdf_path)
                
                # Clean the text
                cleaned_text = self.clean_text(raw_text)
                
                # Store processed document
                doc_data = {
                    "filename": pdf_path.name,
                    "raw_text": raw_text,
                    "cleaned_text": cleaned_text,
                    "metadata": metadata,
                    "processing_stats": {
                        "raw_char_count": len(raw_text),
                        "cleaned_char_count": len(cleaned_text),
                        "compression_ratio": len(cleaned_text) / len(raw_text) if raw_text else 0
                    }
                }
                
                processed_docs[pdf_path.name] = doc_data
                total_chars += len(cleaned_text)
                total_pages += metadata.get("pages_with_text", 0)
                
                logger.info(f"✓ Processed {pdf_path.name}: {len(cleaned_text)} chars, {metadata.get('pages_with_text', 0)} pages")
                
            except Exception as e:
                logger.error(f"✗ Failed to process {pdf_path.name}: {e}")
                continue
        
        # Store processing summary
        self.processed_docs = processed_docs
        
        logger.info(f"📊 Processing complete: {len(processed_docs)} files, {total_chars:,} total characters, {total_pages} pages")
        
        return processed_docs
    
    def get_document_by_filename(self, filename: str) -> Optional[Dict]:
        """
        Get processed document data by filename.
        
        Args:
            filename: Name of the PDF file
            
        Returns:
            Document data or None if not found
        """
        return self.processed_docs.get(filename)
    
    def get_all_documents(self) -> Dict[str, Dict]:
        """
        Get all processed documents.
        
        Returns:
            Dictionary of all processed documents
        """
        return self.processed_docs
    
    def get_combined_text(self) -> str:
        """
        Get all cleaned text combined into a single string.
        
        Returns:
            Combined cleaned text from all documents
        """
        combined_text = ""
        for doc_data in self.processed_docs.values():
            combined_text += f"\n\n=== {doc_data['filename']} ===\n\n"
            combined_text += doc_data['cleaned_text']
        
        return combined_text.strip()
    
    def save_processed_text(self, output_dir: Optional[Path] = None) -> Path:
        """
        Save processed text to files for inspection.
        
        Args:
            output_dir: Directory to save files (defaults to data/processed_texts)
            
        Returns:
            Path to the output directory
        """
        if output_dir is None:
            output_dir = Config.DATA_DIR / "processed_texts"
        
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Save individual processed files
        for filename, doc_data in self.processed_docs.items():
            output_file = output_dir / f"{filename}_processed.txt"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"=== {filename} ===\n\n")
                f.write(f"Metadata:\n{doc_data['metadata']}\n\n")
                f.write(f"Cleaned Text:\n{doc_data['cleaned_text']}")
        
        # Save combined text
        combined_file = output_dir / "all_documents_combined.txt"
        with open(combined_file, 'w', encoding='utf-8') as f:
            f.write(self.get_combined_text())
        
        logger.info(f"💾 Saved processed texts to {output_dir}")
        return output_dir
