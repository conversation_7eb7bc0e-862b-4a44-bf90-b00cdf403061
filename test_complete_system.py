"""Complete system integration test for the ManagerPort RAG Chatbot."""

import sys
import time
import requests
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from rag_chatbot.config import Config
from rag_chatbot.rag_pipeline import RAGPipeline
from rag_chatbot.vector_store import VectorStore
from rag_chatbot.utils.logger import logger


def test_configuration():
    """Test configuration and environment setup."""
    print("🔧 Testing Configuration...")
    
    # Check API key
    if not Config.OPENAI_API_KEY:
        print("❌ OpenAI API key not found")
        return False
    print("✅ OpenAI API key configured")
    
    # Check directories
    if not Config.PDF_DIR.exists():
        print("❌ PDF directory not found")
        return False
    print("✅ PDF directory exists")
    
    # Check PDF files
    pdf_files = Config.get_pdf_files()
    if len(pdf_files) == 0:
        print("❌ No PDF files found")
        return False
    print(f"✅ Found {len(pdf_files)} PDF files")
    
    return True


def test_vector_store():
    """Test vector store functionality."""
    print("\n📊 Testing Vector Store...")
    
    try:
        vector_store = VectorStore()
        stats = vector_store.get_collection_stats()
        
        if stats.get('total_documents', 0) == 0:
            print("❌ Vector store is empty")
            return False
        
        print(f"✅ Vector store has {stats['total_documents']} documents")
        print(f"✅ From {stats['unique_source_files']} source files")
        
        # Test search
        results = vector_store.search_similar_documents("projektový manažment", n_results=3)
        if len(results) == 0:
            print("❌ Search returned no results")
            return False
        
        print(f"✅ Search returned {len(results)} results")
        return True
        
    except Exception as e:
        print(f"❌ Vector store test failed: {e}")
        return False


def test_rag_pipeline():
    """Test RAG pipeline functionality."""
    print("\n🤖 Testing RAG Pipeline...")
    
    try:
        rag = RAGPipeline()
        
        # Test pipeline info
        info = rag.get_pipeline_info()
        if info.get('status') != 'ready':
            print("❌ RAG pipeline not ready")
            return False
        
        print(f"✅ RAG pipeline ready with model: {info['model']}")
        
        # Test question answering
        test_question = "Čo je projektový manažment?"
        response = rag.ask_question(test_question)
        
        if 'error' in response:
            print(f"❌ RAG pipeline error: {response['error']}")
            return False
        
        if len(response['answer']) < 50:
            print("❌ RAG response too short")
            return False
        
        print(f"✅ RAG pipeline generated {len(response['answer'])} character response")
        print(f"✅ Found {len(response['sources'])} source documents")
        
        return True
        
    except Exception as e:
        print(f"❌ RAG pipeline test failed: {e}")
        return False


def test_gradio_interface():
    """Test if Gradio interface is accessible."""
    print("\n🌐 Testing Gradio Interface...")
    
    try:
        # Check if the interface is running
        response = requests.get("http://localhost:7860", timeout=5)
        
        if response.status_code == 200:
            print("✅ Gradio interface is accessible")
            print("✅ Web interface responding correctly")
            return True
        else:
            print(f"❌ Gradio interface returned status {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("⚠️ Gradio interface not running (this is expected if not started)")
        print("   Run 'python app.py' to start the interface")
        return True  # Not a failure, just not running
    except Exception as e:
        print(f"❌ Gradio interface test failed: {e}")
        return False


def main():
    """Run complete system test."""
    print("🧪 ManagerPort RAG Chatbot - Complete System Test")
    print("=" * 60)
    
    tests = [
        ("Configuration", test_configuration),
        ("Vector Store", test_vector_store),
        ("RAG Pipeline", test_rag_pipeline),
        ("Gradio Interface", test_gradio_interface),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready to use.")
        print("\n🚀 To start the chat interface, run:")
        print("   python app.py")
        print("\n🌐 Then open: http://localhost:7860")
    else:
        print("⚠️ Some tests failed. Please check the configuration and setup.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
