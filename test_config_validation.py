"""Test script for configuration validation."""

import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from rag_chatbot.config_validator import validate_config


def main():
    """Test configuration validation."""
    print("🔧 Testing Configuration Validation")
    print("=" * 50)
    
    is_valid = validate_config()
    
    if is_valid:
        print("\n✅ Configuration validation passed!")
    else:
        print("\n❌ Configuration validation failed!")
        print("Please fix the errors above before proceeding.")
    
    return is_valid


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
